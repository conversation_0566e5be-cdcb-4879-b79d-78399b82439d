import * as React from 'react'
import {
  Badge,
  Box,
  Button,
  HStack,
  IconButton,
  Kbd,
  Menu,
  MenuButton,
  MenuDivider,
  MenuGroup,
  MenuItem,
  MenuList,
  SimpleGrid,
  Text,
  Tooltip,
  useBreakpointValue,
} from '@chakra-ui/react'
import { FaGithub } from 'react-icons/fa'

import { useRouter } from 'next/router'

import headerNav from '@/data/header-nav'
import NavLink from '@/components/nav-link'
import { useScrollSpy } from '@/hooks/use-scrollspy'
import { MobileNavButton } from '@/docs/components/mobile-nav'
import { MobileNavContent } from '@/docs/components/mobile-nav'
import { useDisclosure, useUpdateEffect } from '@chakra-ui/react'

import ThemeToggle from './theme-toggle'
import { ProductLaneLogo } from '../logos/productlane'
import { ChevronDownIcon, SearchInput, useHotkeys } from '@saas-ui/react'

import { GlobalSearch } from '../global-search/global-search'
import { useAuth } from '@saas-ui/auth'
import Link from 'next/link'
import {
  LuBookMarked,
  LuBookOpen,
  LuFileCode,
  LuFileText,
  LuGanttChartSquare,
  LuRotateCw,
} from 'react-icons/lu'
import { NextjsIcon } from '../logos/nextjs'

const Header = () => {
  const router = useRouter()
  const mobileNav = useDisclosure()
  const isDesktop = useBreakpointValue({ xl: true })
  const { user, isAuthenticated, logOut } = useAuth()
  const activeId = useScrollSpy(
    headerNav.filter(({ id }) => id).map(({ id }) => `[id="${id}"]`),
    {
      threshold: 0.75,
    }
  )

  const mobileNavBtnRef = React.useRef<HTMLButtonElement>()

  useUpdateEffect(() => {
    mobileNavBtnRef.current?.focus()
  }, [mobileNav.isOpen])

  const { isOpen, onOpen, onClose } = useDisclosure()

  useHotkeys(['/', 'CMD+K'], () => {
    onOpen()
  })

  const isActive = (href: string) => !!router.asPath.match(new RegExp(href))

  return (
    <HStack flex="1" ps="4">
      <HStack spacing="1" flexShrink={0} flex="1" justifyContent="flex-start">
        <NavLink href="/" isActive={isActive('/')} label="首页" />
      </HStack>
      <HStack>
        <ThemeToggle />
      </HStack>
    </HStack>
  )
}

export default Header

function DropdownMenu(props: { label: string; children: React.ReactNode }) {
  const disclosure = useDisclosure()

  return (
    <Box onMouseLeave={(e) => disclosure.onClose()}>
      <Menu {...disclosure} gutter={0} placement="bottom">
        <MenuButton
          as={Button}
          variant="nav-link"
          onMouseEnter={() => disclosure.onOpen()}
        >
          {props.label}
        </MenuButton>
        <MenuList>{props.children}</MenuList>
      </Menu>
    </Box>
  )
}
