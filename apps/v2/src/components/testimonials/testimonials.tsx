import Section from '@/components/marketing/section-wrapper'
import SectionTitle from '@/components/marketing/section-title'
import { SimpleGrid, Stack } from '@chakra-ui/react'

import { Testimonial } from './testimonial'

export const Testimonials = () => {
  return (
    <Section innerWidth="container.xl">
      <SectionTitle title="智能投放系统客户评价" />
      <SimpleGrid columns={[1, 1, 2, 3]} spacing="8">
        <Stack spacing="8">
          <Testimonial
            name="张经理"
            avatar="/testimonials/customer1.jpg"
            description="中国人寿代理平台负责人"
            fontSize="md"
          >
            "我们使用这套智能投放系统已经半年多了，客户转化率提升了40%，
            系统操作简单直观，数据分析功能特别强大，帮助我们精准定位目标客户。
            团队的技术支持也很及时，强烈推荐给同行使用。"
          </Testimonial>
          <Testimonial
            name="李总监"
            description="平安保险数字化营销总监"
            avatar="/testimonials/customer2.jpg"
            fontSize="md"
          >
            "这个投放系统真的改变了我们的营销方式，智能算法推荐的客户群体
            精准度很高，ROI比之前提升了60%。界面设计很人性化，
            新员工上手很快，是我们数字化转型的重要工具。"
          </Testimonial>
        </Stack>
        <Stack spacing="8">
          <Testimonial
            name="王主任"
            description="太平洋保险渠道管理主任"
            avatar="/testimonials/customer3.jpg"
            fontSize="md"
          >
            "系统的实时数据监控功能让我们能够及时调整投放策略，
            客户画像分析帮助我们更好地了解目标群体。
            自动化的投放流程大大提高了工作效率，节省了人力成本。"
          </Testimonial>
          <Testimonial
            name="陈经理"
            description="新华保险业务发展经理"
            avatar="/testimonials/customer4.jpg"
            fontSize="md"
          >
            "使用这套系统后，我们的获客成本降低了30%，
            而且客户质量明显提升。多渠道整合投放功能特别实用，
            一个平台就能管理所有的营销活动，大大简化了工作流程。"
          </Testimonial>
        </Stack>
        <Stack spacing="8">
          <Testimonial
            name="赵总"
            description="泰康保险电商部总经理"
            avatar="/testimonials/customer5.jpg"
            fontSize="md"
          >
            "智能投放系统的数据分析报告非常详细，帮助我们深入了解客户需求。
            系统的A/B测试功能让我们能够优化投放策略，提高转化效果。
            整体使用体验很好，是我们营销团队不可缺少的工具。"
          </Testimonial>
          <Testimonial
            name="孙主管"
            description="阳光保险渠道拓展主管"
            avatar="/testimonials/customer6.jpg"
            fontSize="md"
          >
            "这套系统的智能推荐算法真的很厉害，能够精准匹配我们的目标客户。
            多平台统一管理功能让我们的工作效率提升了很多，
            客户响应率比以前提高了50%以上。"
          </Testimonial>
        </Stack>
      </SimpleGrid>
    </Section>
  )
}
