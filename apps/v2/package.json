{"name": "website-v2", "version": "2.4.14", "private": true, "author": "Eelco Wiersma <<EMAIL>>", "scripts": {"dev": "concurrently \"contentlayer2 dev\" \"next dev --port 3020\"", "dev:next": "next dev --port 3020", "dev:contentlayer": "contentlayer2 dev", "build": "yarn gen:contentlayer && next build", "start": "next start", "lint": "next lint", "lint:staged": "lint-staged --allow-empty --config ../../lint-staged.config.js", "prettify": "prettier --write src", "gen:theme-typings": "chakra-cli tokens src/styles/theme.ts", "gen:contentlayer": "contentlayer2 build --config ./contentlayer.config.ts --verbose", "typecheck": "tsc --noEmit"}, "dependencies": {"@ark-ui/react": "^4.4.4", "@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.2.0", "@chakra-ui/props-docs": "^2.2.1", "@chakra-ui/react": "^3.20.0", "@chakra-ui/react-env": "^3.1.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource-variable/inter": "^5.1.0", "@mapbox/rehype-prism": "^0.9.0", "@mdx-js/loader": "^2.3.0", "@next/bundle-analyzer": "^14.2.14", "@octokit/rest": "^21.0.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@saas-ui-pro/react": "next", "@saas-ui/command-bar": "workspace:^", "@saas-ui/file-upload": "workspace:*", "@saas-ui/forms": "workspace:*", "@saas-ui/palette": "workspace:*", "@saas-ui/props-docs": "workspace:*", "@saas-ui/react": "workspace:*", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@svgr/webpack": "^5.5.0", "@vercel/og": "^0.6.3", "canvas-confetti": "^1.9.3", "change-case": "^5.4.4", "contentlayer2": "^0.5.3", "date-fns": "^3.6.0", "framer-motion": "^11.9.0", "github-slugger": "^2.0.0", "match-sorter": "^6.3.4", "next": "^15.3.2", "next-contentlayer2": "^0.5.3", "next-mdx-remote": "^4.4.1", "next-seo": "^6.6.0", "prism-react-renderer": "^1.3.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-frame-component": "^5.2.7", "react-icons": "^5.3.0", "react-intersection-observer": "^9.13.1", "react-live-runner": "^1.0.7", "react-markdown": "^8.0.7", "react-shadow": "^20.5.0", "reading-time": "^1.5.0", "rehype-slug": "^6.0.0", "remark-code-titles": "^0.1.2", "remark-emoji": "^3.1.2", "remark-gfm": "^4.0.0", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@netlify/plugin-nextjs": "^4.41.3", "@types/node": "^18.19.54", "@types/react": "^18.3.11", "concurrently": "^8.2.2", "electron-to-chromium": "^1.5.31", "eslint": "^8.57.1", "eslint-config-next": "^14.2.14", "file-loader": "^6.2.0", "typescript": "^5.6.2", "webpack": "^5.95.0"}}