{"name": "palette-docs", "version": "1.5.12", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:staged": "lint-staged --allow-empty --config ../../lint-staged.config.js", "typecheck": "tsc --noEmit"}, "dependencies": {"@chakra-ui/react": "^3.20.0", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^5.0.20", "@saas-ui/next-workspaces": "^0.3.0", "@saas-ui/palette": "workspace:*", "@saas-ui/react": "workspace:*", "@svgr/webpack": "^5.5.0", "@types/lodash.debounce": "^4.0.9", "framer-motion": "^11.3.28", "lodash.debounce": "^4.0.8", "next": "^15.3.2", "next-seo": "^6.5.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0"}, "devDependencies": {"@types/react": "^18.3.3", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "typescript": "^5.5.4"}}