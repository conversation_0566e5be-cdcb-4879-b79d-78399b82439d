'use client'

import { Box, Container, Grid, HStack, Heading, Text } from '@chakra-ui/react'
import { Persona } from '@saas-ui/react'

export function FounderSection() {
  return (
    <Box py="20" borderBottomWidth="1px" borderStyle="dashed">
      <Container maxW="8xl">
        <Text textStyle="sm" fontWeight="medium" mb="2">
          为什么选择我们？
        </Text>

        <Heading as="h2" size="4xl" mb="1em">
          专业团队，值得信赖
        </Heading>

        <Grid templateColumns="1fr 1fr" gap="16">
          <Box>
            <Text textStyle="lg" mb="1em" fontWeight="medium" color="fg.subtle">
              保险行业正在经历数字化转型的关键时期，传统的营销方式
              已经无法满足现代客户的需求。如何精准获客、提高转化率、
              降低获客成本，成为每个保险从业者面临的挑战。
            </Text>

            <Text textStyle="lg" mb="1em" fontWeight="medium" color="fg.subtle">
              我们的智能投放系统应运而生，通过大数据分析和AI算法，
              为保险行业提供精准的客户匹配服务，让每一次投放都更有价值，
              让每一个客户需求都能得到最优质的服务。
            </Text>
          </Box>
          <Box>
            <Text textStyle="lg" mb="1em" fontWeight="medium" color="fg.subtle">
              结果如何？保险公司获得了更精准的客户，代理人提高了成交率，
              客户得到了更贴心的服务。我们用技术连接需求与服务，
              打造多方共赢的保险生态，让保险服务更智能、更高效。
            </Text>




          </Box>
        </Grid>
      </Container>
    </Box>
  )
}
