'use client'

import {
  Badge,
  Box,
  Container,
  HStack,
  Heading,
  Stack,
  Text,
} from '@chakra-ui/react'
import {
  LuSmartphone,
  LuBrain,
  LuBarChart3,
  LuCheckCircle
} from 'react-icons/lu'

export function ProductsSection() {
  return (
    <Box as="section" py="20" borderTopWidth="1px" borderStyle="dashed">
      <Container maxW="8xl">
        <Stack gap="16" alignItems="center">
          {/* 标题区域 */}
          <Stack gap="4" alignItems="center" textAlign="center" maxW="4xl">
            <Heading as="h2" fontSize="5xl" lineHeight="1.1">
              完整的保险流量投放解决方案
            </Heading>
            <Text fontSize="xl" color="fg.subtle">
              从流量获取到数据分析，一站式解决保险行业数字化营销需求
            </Text>
          </Stack>

          {/* 步骤展示区域 */}
          <Box w="full" maxW="6xl">
            <HStack gap="8" justifyContent="space-between" alignItems="center">
              <Box textAlign="center" flex="1">
                <Box
                  w="12"
                  h="12"
                  bg="blue.500"
                  color="white"
                  borderRadius="full"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  mx="auto"
                  mb="3"
                  fontSize="lg"
                  fontWeight="bold"
                >
                  1
                </Box>
                <Heading as="h3" fontSize="lg" mb="2">流量聚合</Heading>
                <Text fontSize="sm" color="fg.subtle">微信小程序聚合保险需求</Text>
              </Box>

              <Box w="16" h="0.5" bg="gray.200" />

              <Box textAlign="center" flex="1">
                <Box
                  w="12"
                  h="12"
                  bg="green.500"
                  color="white"
                  borderRadius="full"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  mx="auto"
                  mb="3"
                  fontSize="lg"
                  fontWeight="bold"
                >
                  2
                </Box>
                <Heading as="h3" fontSize="lg" mb="2">智能匹配</Heading>
                <Text fontSize="sm" color="fg.subtle">AI算法精准匹配服务商</Text>
              </Box>

              <Box w="16" h="0.5" bg="gray.200" />

              <Box textAlign="center" flex="1">
                <Box
                  w="12"
                  h="12"
                  bg="purple.500"
                  color="white"
                  borderRadius="full"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  mx="auto"
                  mb="3"
                  fontSize="lg"
                  fontWeight="bold"
                >
                  3
                </Box>
                <Heading as="h3" fontSize="lg" mb="2">数据分析</Heading>
                <Text fontSize="sm" color="fg.subtle">实时监控优化投放效果</Text>
              </Box>

              <Box w="16" h="0.5" bg="gray.200" />

              <Box textAlign="center" flex="1">
                <Box
                  w="12"
                  h="12"
                  bg="teal.500"
                  color="white"
                  borderRadius="full"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  mx="auto"
                  mb="3"
                  fontSize="lg"
                  fontWeight="bold"
                >
                  4
                </Box>
                <Heading as="h3" fontSize="lg" mb="2">成功转化</Heading>
                <Text fontSize="sm" color="fg.subtle">多方共赢的服务生态</Text>
              </Box>
            </HStack>
          </Box>
          {/* 详细步骤内容 */}
          <Stack gap="12" w="full" maxW="6xl">
            {/* 步骤1：流量聚合 */}
            <HStack gap="8" alignItems="flex-start">
              <Box
                w="16"
                h="16"
                bg="blue.50"
                borderRadius="xl"
                display="flex"
                alignItems="center"
                justifyContent="center"
                flexShrink="0"
              >
                <LuSmartphone size="32" color="#3182ce" />
              </Box>
              <Stack gap="3" flex="1">
                <HStack gap="3">
                  <Heading as="h3" fontSize="2xl">
                    聚合保险需求流量
                  </Heading>
                  <Badge
                    size="sm"
                    variant="outline"
                    colorPalette="blue"
                    borderRadius="full"
                  >
                    微信生态
                  </Badge>
                </HStack>
                <Text fontSize="lg" color="fg.subtle" lineHeight="1.6">
                  通过微信小程序作为主要流量入口，聚合车险、财险、增值服务等多种保险需求。
                  用户可以便捷地选择服务类型，填写基本信息，一站式解决保险需求。
                  基于微信生态的天然优势，降低用户获取成本，提升转化效率。
                </Text>
              </Stack>
            </HStack>

            {/* 步骤2：智能匹配 */}
            <HStack gap="8" alignItems="flex-start">
              <Box
                w="16"
                h="16"
                bg="green.50"
                borderRadius="xl"
                display="flex"
                alignItems="center"
                justifyContent="center"
                flexShrink="0"
              >
                <LuBrain size="32" color="#38a169" />
              </Box>
              <Stack gap="3" flex="1">
                <HStack gap="3">
                  <Heading as="h3" fontSize="2xl">
                    智能匹配优质服务商
                  </Heading>
                  <Badge
                    size="sm"
                    variant="outline"
                    colorPalette="green"
                    borderRadius="full"
                  >
                    AI驱动
                  </Badge>
                </HStack>
                <Text fontSize="lg" color="fg.subtle" lineHeight="1.6">
                  基于用户地域、需求类型、历史数据等多维度信息，智能推荐最适合的保险公司和服务商。
                  通过机器学习算法不断优化匹配精度，提升客户满意度和转化率。
                  支持多种保险产品类型，覆盖个人和企业客户需求。
                </Text>
              </Stack>
            </HStack>

            {/* 步骤3：数据分析 */}
            <HStack gap="8" alignItems="flex-start">
              <Box
                w="16"
                h="16"
                bg="purple.50"
                borderRadius="xl"
                display="flex"
                alignItems="center"
                justifyContent="center"
                flexShrink="0"
              >
                <LuBarChart3 size="32" color="#805ad5" />
              </Box>
              <Stack gap="3" flex="1">
                <HStack gap="3">
                  <Heading as="h3" fontSize="2xl">
                    数据驱动优化投放
                  </Heading>
                  <Badge
                    size="sm"
                    variant="outline"
                    colorPalette="purple"
                    borderRadius="full"
                  >
                    实时分析
                  </Badge>
                </HStack>
                <Text fontSize="lg" color="fg.subtle" lineHeight="1.6">
                  全方位数据监控和分析，包括点击率、转化率、用户行为轨迹等关键指标。
                  通过数据洞察持续优化投放策略和用户体验。
                  3D可视化数据大屏实时展示业务状况，帮助决策者快速了解运营情况。
                </Text>
              </Stack>
            </HStack>

            {/* 步骤4：成功转化 */}
            <HStack gap="8" alignItems="flex-start">
              <Box
                w="16"
                h="16"
                bg="teal.50"
                borderRadius="xl"
                display="flex"
                alignItems="center"
                justifyContent="center"
                flexShrink="0"
              >
                <LuCheckCircle size="32" color="#319795" />
              </Box>
              <Stack gap="3" flex="1">
                <HStack gap="3">
                  <Heading as="h3" fontSize="2xl">
                    多方共赢服务生态
                  </Heading>
                  <Badge
                    size="sm"
                    variant="outline"
                    colorPalette="teal"
                    borderRadius="full"
                  >
                    生态共赢
                  </Badge>
                </HStack>
                <Text fontSize="lg" color="fg.subtle" lineHeight="1.6">
                  连接用户、保险公司、服务商的完整生态链。用户获得便捷服务，保险公司获得优质客源，
                  服务商获得精准订单，平台获得可持续发展。通过技术手段降低各方成本，
                  提升服务效率，实现真正的多方共赢。
                </Text>
              </Stack>
            </HStack>
          </Stack>
        </Stack>
      </Container>
    </Box>
  )
}
