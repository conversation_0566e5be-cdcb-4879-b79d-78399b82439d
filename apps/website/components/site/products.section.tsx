import {
  Box,
  Container,
  Heading,
  Stack,
  Text,
} from '@chakra-ui/react'

export function ProductsSection() {
  return (
    <Box as="section" py="20" borderTopWidth="1px" borderStyle="dashed">
      <Container maxW="8xl">
        <Stack gap="16" alignItems="center">
          {/* 标题区域 */}
          <Stack gap="4" alignItems="center" textAlign="center" maxW="4xl">
            <Heading as="h2" fontSize="5xl" lineHeight="1.1">
              智能投放系统流程
            </Heading>
            <Text fontSize="xl" color="fg.subtle">
              从客户需求到服务交付的完整流程，实现高效精准的保险服务匹配
            </Text>
          </Stack>

          {/* 时间线展示区域 */}
          <Box w="full" maxW="4xl">
            <Stack gap="12" position="relative" pl="8">
              {/* 垂直连接线 */}
              <Box
                position="absolute"
                left="6"
                top="0"
                bottom="0"
                width="2px"
                bg="gray.200"
                zIndex="0"
              />

              {/* 步骤1：客户提交需求 */}
              <Box position="relative">
                <Box
                  position="absolute"
                  left="-8"
                  top="2"
                  w="12"
                  h="12"
                  bg="blue.500"
                  borderRadius="full"
                  zIndex="1"
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    客户提交需求
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    用户通过微信小程序填写保险需求信息
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 选择保险类型（车险、财险等）</Text>
                    <Text>• 填写基本信息和联系方式</Text>
                  </Stack>
                </Stack>
              </Box>

              {/* 步骤2：智能算法匹配 */}
              <Box position="relative">
                <Box
                  position="absolute"
                  left="-8"
                  top="2"
                  w="12"
                  h="12"
                  bg="green.500"
                  borderRadius="full"
                  zIndex="1"
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    智能算法匹配
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    系统自动分析客户需求，匹配最适合的服务商
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 基于地理位置就近匹配</Text>
                    <Text>• 根据服务商专业度排序</Text>
                  </Stack>
                </Stack>
              </Box>

              {/* 步骤3：实时推送通知 */}
              <Box position="relative">
                <Box
                  position="absolute"
                  left="-8"
                  top="2"
                  w="12"
                  h="12"
                  bg="orange.500"
                  borderRadius="full"
                  zIndex="1"
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    实时推送通知
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    向匹配的服务商实时推送客户需求
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 多渠道通知（短信、微信、APP）</Text>
                    <Text>• 5分钟内必须响应</Text>
                  </Stack>
                </Stack>
              </Box>

              {/* 步骤4：服务商响应 */}
              <Box position="relative">
                <Box
                  position="absolute"
                  left="-8"
                  top="2"
                  w="12"
                  h="12"
                  bg="purple.500"
                  borderRadius="full"
                  zIndex="1"
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    服务商响应
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    服务商主动联系客户，提供专业服务
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 平均响应时间：2分钟</Text>
                    <Text>• 客户满意度：98.5%</Text>
                  </Stack>
                </Stack>
              </Box>
            </Stack>
          </Box>
        </Stack>
      </Container>
    </Box>
  )
}