import React from 'react'
import {
  Box,
  Container,
  Heading,
  Stack,
  Text,
} from '@chakra-ui/react'

export function ProductsSection() {
  return (
    <section style={{ paddingTop: '5rem', paddingBottom: '5rem', borderTop: '1px dashed #e2e8f0' }}>
      <Container maxW="8xl">
        <Stack gap="16" alignItems="center">
          {/* 标题区域 */}
          <Stack gap="4" alignItems="center" textAlign="center" maxW="4xl">
            <Heading as="h2" fontSize="5xl" lineHeight="1.1">
              智能投放系统流程
            </Heading>
            <Text fontSize="xl" color="fg.subtle">
              从客户需求到服务交付的完整流程，实现高效精准的保险服务匹配
            </Text>
          </Stack>

          {/* 时间线展示区域 */}
          <div style={{ width: '100%', maxWidth: '64rem' }}>
            <Stack gap="12" position="relative" pl="8">
              {/* 垂直连接线 */}
              <div
                style={{
                  position: 'absolute',
                  left: '1.5rem',
                  top: '0',
                  bottom: '0',
                  width: '2px',
                  backgroundColor: '#e2e8f0',
                  zIndex: 0
                }}
              />

              {/* 步骤1：客户提交需求 */}
              <div style={{ position: 'relative' }}>
                <div
                  style={{
                    position: 'absolute',
                    left: '-2rem',
                    top: '0.5rem',
                    width: '3rem',
                    height: '3rem',
                    backgroundColor: '#3182ce',
                    borderRadius: '50%',
                    zIndex: 1
                  }}
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    客户提交需求
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    用户通过微信小程序填写保险需求信息
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 选择保险类型（车险、财险等）</Text>
                    <Text>• 填写基本信息和联系方式</Text>
                  </Stack>
                </Stack>
              </div>

              {/* 步骤2：智能算法匹配 */}
              <div style={{ position: 'relative' }}>
                <div
                  style={{
                    position: 'absolute',
                    left: '-2rem',
                    top: '0.5rem',
                    width: '3rem',
                    height: '3rem',
                    backgroundColor: '#38a169',
                    borderRadius: '50%',
                    zIndex: 1
                  }}
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    智能算法匹配
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    系统自动分析客户需求，匹配最适合的服务商
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 基于地理位置就近匹配</Text>
                    <Text>• 根据服务商专业度排序</Text>
                  </Stack>
                </Stack>
              </div>

              {/* 步骤3：实时推送通知 */}
              <div style={{ position: 'relative' }}>
                <div
                  style={{
                    position: 'absolute',
                    left: '-2rem',
                    top: '0.5rem',
                    width: '3rem',
                    height: '3rem',
                    backgroundColor: '#dd6b20',
                    borderRadius: '50%',
                    zIndex: 1
                  }}
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    实时推送通知
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    向匹配的服务商实时推送客户需求
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 多渠道通知（短信、微信、APP）</Text>
                    <Text>• 5分钟内必须响应</Text>
                  </Stack>
                </Stack>
              </div>

              {/* 步骤4：服务商响应 */}
              <div style={{ position: 'relative' }}>
                <div
                  style={{
                    position: 'absolute',
                    left: '-2rem',
                    top: '0.5rem',
                    width: '3rem',
                    height: '3rem',
                    backgroundColor: '#805ad5',
                    borderRadius: '50%',
                    zIndex: 1
                  }}
                /></Box>
                <Stack gap="3">
                  <Heading as="h3" fontSize="xl" fontWeight="bold">
                    服务商响应
                  </Heading>
                  <Text fontSize="lg" color="fg.subtle">
                    服务商主动联系客户，提供专业服务
                  </Text>
                  <Stack gap="2" fontSize="md" color="fg.muted" pl="4">
                    <Text>• 平均响应时间：2分钟</Text>
                    <Text>• 客户满意度：98.5%</Text>
                  </Stack>
                </Stack>
              </div>
            </Stack>
          </div>
        </Stack>
      </Container>
    </section>
  )
}