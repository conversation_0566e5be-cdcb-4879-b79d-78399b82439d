'use client'

import {
  Badge,
  Box,
  Container,
  Heading,
  Stack,
  Text,
} from '@chakra-ui/react'
import { Timeline } from '@saas-ui/react'


export function ProductsSection() {
  return (
    <Box as="section" py="20" borderTopWidth="1px" borderStyle="dashed">
      <Container maxW="8xl">
        <Stack gap="16" alignItems="center">
          {/* 标题区域 */}
          <Stack gap="4" alignItems="center" textAlign="center" maxW="4xl">
            <Heading as="h2" fontSize="5xl" lineHeight="1.1">
              智能投放系统流程
            </Heading>
            <Text fontSize="xl" color="fg.subtle">
              从客户需求到服务交付的完整流程，实现高效精准的保险服务匹配
            </Text>
          </Stack>

          {/* 时间线展示区域 */}
          <Box w="full" maxW="4xl">
            <Timeline.Root size="lg" variant="subtle">
              {/* 步骤1：客户提交需求 */}
              <Timeline.Item>
                <Timeline.Connector>
                  📱
                </Timeline.Connector>
                <Timeline.Content>
                  <Timeline.Title fontSize="xl" fontWeight="bold" mb="2">
                    客户提交需求
                  </Timeline.Title>
                  <Timeline.Description fontSize="lg" color="fg.subtle" mb="3">
                    用户通过微信小程序填写保险需求信息
                  </Timeline.Description>
                  <Stack gap="2" fontSize="md" color="fg.muted">
                    <Text>• 选择保险类型（车险、财险等）</Text>
                    <Text>• 填写基本信息和联系方式</Text>
                  </Stack>
                </Timeline.Content>
              </Timeline.Item>

              {/* 步骤2：智能算法匹配 */}
              <Timeline.Item>
                <Timeline.Connector>
                  🤖
                </Timeline.Connector>
                <Timeline.Content>
                  <Timeline.Title fontSize="xl" fontWeight="bold" mb="2">
                    智能算法匹配
                  </Timeline.Title>
                  <Timeline.Description fontSize="lg" color="fg.subtle" mb="3">
                    系统自动分析客户需求，匹配最适合的服务商
                  </Timeline.Description>
                  <Stack gap="2" fontSize="md" color="fg.muted">
                    <Text>• 基于地理位置就近匹配</Text>
                    <Text>• 根据服务商专业度排序</Text>
                  </Stack>
                </Timeline.Content>
              </Timeline.Item>

              {/* 步骤3：实时推送通知 */}
              <Timeline.Item>
                <Timeline.Connector>
                  🔔
                </Timeline.Connector>
                <Timeline.Content>
                  <Timeline.Title fontSize="xl" fontWeight="bold" mb="2">
                    实时推送通知
                  </Timeline.Title>
                  <Timeline.Description fontSize="lg" color="fg.subtle" mb="3">
                    向匹配的服务商实时推送客户需求
                  </Timeline.Description>
                  <Stack gap="2" fontSize="md" color="fg.muted">
                    <Text>• 多渠道通知（短信、微信、APP）</Text>
                    <Text>• 5分钟内必须响应</Text>
                  </Stack>
                </Timeline.Content>
              </Timeline.Item>

              {/* 步骤4：服务商响应 */}
              <Timeline.Item>
                <Timeline.Connector>
                  📞
                </Timeline.Connector>
                <Timeline.Content>
                  <Timeline.Title fontSize="xl" fontWeight="bold" mb="2">
                    服务商响应
                  </Timeline.Title>
                  <Timeline.Description fontSize="lg" color="fg.subtle" mb="3">
                    服务商主动联系客户，提供专业服务
                  </Timeline.Description>
                  <Stack gap="2" fontSize="md" color="fg.muted">
                    <Text>• 平均响应时间：2分钟</Text>
                    <Text>• 客户满意度：98.5%</Text>
                  </Stack>
                </Timeline.Content>
              </Timeline.Item>
            </Timeline.Root>
        </Stack>
      </Container>
    </Box>
  )
}
