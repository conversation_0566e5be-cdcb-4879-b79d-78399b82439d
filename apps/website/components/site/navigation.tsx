import { HStack, Stack, Text } from '@chakra-ui/react'

import {
  List,
  ListItem,
  NavigationMenuContent,
  NavigationMenuIndicator,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuRoot,
  NavigationMenuTrigger,
  NavigationMenuViewport,
  ViewportPosition,
} from '../navigation-menu'

export const Navigation = () => {
  return (
    <NavigationMenuRoot>
      <NavigationMenuList>
        <HStack display={{ base: 'none', md: 'flex' }} gap="1px">
          <NavigationMenuItem>
            <NavigationMenuLink href="/">首页</NavigationMenuLink>
          </NavigationMenuItem>
        </HStack>
        <NavigationMenuIndicator></NavigationMenuIndicator>
      </NavigationMenuList>

      <ViewportPosition>
        <NavigationMenuViewport />
      </ViewportPosition>
    </NavigationMenuRoot>
  )
}
