import { Section, SimpleGrid, Stack } from '@saas-ui/react'

import { Testimonial } from './testimonial'

export const Testimonials = () => {
  return (
    <Section.Root>
      <Section.Title
        as="h3"
        textStyle="4xl"
        lineHeight="1.2"
        textAlign="center"
        mb="16"
      >
        智能投放系统客户评价
      </Section.Title>
      <SimpleGrid columns={[1, 1, 2, 3]} gap="8">
        <Stack gap="8">
          <Testimonial
            name="张经理"
            avatar="/testimonials/customer1.jpg"
            description="中国人寿代理平台负责人"
            fontSize="md"
          >
            “我们使用这套智能投放系统已经半年多了，客户转化率提升了40%，
            系统操作简单直观，数据分析功能特别强大，帮助我们精准定位目标客户。
            团队的技术支持也很及时，强烈推荐给同行使用。”
          </Testimonial>
          <Testimonial
            name="李总监"
            description="平安保险数字化营销总监"
            avatar="/testimonials/customer2.jpg"
            fontSize="md"
          >
            “I really recommend SaaSUI to any developer or team seeking a
            robust, visually appealing, and easy-to-implement UI framework. The
            support and updates from the SaaSUI team were exceptional, Thank
            you. ”
          </Testimonial>
          <Testimonial
            name="刘总"
            description="华夏保险电销中心总监"
            avatar="/testimonials/customer7.jpg"
            fontSize="md"
          >
            “系统的客户画像功能非常精准，帮助我们快速识别高价值客户。
            自动化的跟进提醒让我们不会错过任何一个潜在客户，
            团队的整体业绩提升了35%，非常满意这个系统。”
          </Testimonial>
          <Testimonial
            name="马经理"
            description="友邦保险区域经理"
            avatar="/testimonials/customer8.jpg"
            fontSize="md"
          >
            “这个平台的移动端适配做得特别好，我们的代理人在外出展业时
            可以随时查看客户信息和跟进记录。系统的响应速度很快，
            即使在网络不好的情况下也能正常使用，大大提升了工作效率。”
          </Testimonial>
        </Stack>
        <Stack gap="8">
          <Testimonial
            name="周主任"
            description="中国太保营销管理主任"
            avatar="/testimonials/customer9.jpg"
            fontSize="md"
          >
            “系统的权限管理功能很完善，不同级别的员工可以看到不同的数据，
            既保证了信息安全，又提高了工作效率。客户满意度调查功能
            帮助我们及时了解服务质量，持续改进我们的服务水平。”
          </Testimonial>
          <Testimonial
            name="吴总监"
            description="大地保险科技总监"
            avatar="/testimonials/customer10.jpg"
            fontSize="md"
          >
            “作为保险科技部门，我们对系统的稳定性和扩展性要求很高。
            这套智能投放系统完全满足了我们的需求，API接口丰富，
            可以很好地与我们现有的核心系统集成。技术架构设计合理，
            为我们的数字化转型提供了强有力的技术支撑。”
          </Testimonial>
          <Testimonial
            name="林经理"
            description="安盛天平车险部经理"
            avatar="/testimonials/customer11.jpg"
            fontSize="md"
          >
            “车险业务的时效性要求很高，这个系统的实时通知功能帮了大忙。
            客户一提交需求，我们就能立即收到推送，快速响应客户需求。
            系统的地图功能可以显示客户位置，方便我们安排就近的服务人员，
            大大提升了客户体验和服务效率。”
          </Testimonial>
          <Testimonial
            name="黄主管"
            description="人保财险网销主管"
            avatar="/testimonials/customer12.jpg"
            fontSize="md"
          >
            “系统的财务统计功能让我们的佣金结算变得非常简单，
            每个月的业绩报表自动生成，准确率100%。
            代理人可以实时查看自己的业绩和收入情况，
            极大地提升了团队的工作积极性和透明度。”
          </Testimonial>
        </Stack>

        <Stack gap="8">
          <Testimonial
            name="郑总"
            description="众安保险产品总监"
            avatar="/testimonials/customer13.jpg"
            fontSize="md"
          >
            “作为互联网保险公司，我们对创新产品的推广速度要求很高。
            这个智能投放系统帮助我们快速触达目标客户群体，
            新产品上线后的用户获取效率提升了3倍。
            系统的用户行为分析功能为我们的产品优化提供了宝贵数据。”
          </Testimonial>
          <Testimonial
            name="田经理"
            description="国寿财险渠道经理"
            avatar="/testimonials/customer14.jpg"
            fontSize="md"
          >
            “系统的培训功能对我们新员工的快速上手帮助很大，
            内置的话术库和产品介绍让新人能够快速掌握销售技巧。
            客户标签管理功能帮助我们更好地细分客户群体，
            针对不同客户制定个性化的营销策略。”
          </Testimonial>

          <Testimonial
            name="何总监"
            description="泰康在线运营总监"
            avatar="/testimonials/customer15.jpg"
            fontSize="md"
          >
            “系统的多险种支持功能让我们能够为客户提供一站式保险服务，
            从车险到健康险，客户可以在一个平台上完成所有需求。
            跨部门协作功能提升了我们内部的工作效率，
            客户服务质量得到了显著提升。”
          </Testimonial>
          <Testimonial
            name="邓总"
            description="平安好医生保险事业部总经理"
            avatar="/testimonials/customer16.jpg"
            fontSize="md"
          >
            “作为医疗健康保险的专业平台，我们对数据安全和用户体验要求极高。
            这个智能投放系统在保障数据安全的同时，提供了优秀的用户体验。
            与我们的医疗服务平台无缝集成，为用户提供了从健康管理到保险保障的
            全流程服务，客户满意度达到了98%以上。”
          </Testimonial>
        </Stack>
      </SimpleGrid>
    </Section.Root>
  )
}
