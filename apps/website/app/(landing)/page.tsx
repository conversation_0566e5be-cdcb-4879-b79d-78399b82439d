import { CustomersSection } from '@/components/site/customers.section'
import { FounderSection } from '@/components/site/founder.section'
import { GetStartedSection } from '@/components/site/get-started.section'
import { HeroSection } from '@/components/site/hero.section'
import { ProductsSection } from '@/components/site/products.section'
import { ProductShowcaseSection } from '@/components/site/product-showcase.section'
import { PricingSection } from '@/components/site/pricing.section'
import { ContactSection } from '@/components/site/contact.section'
import { TestimonialsSection } from '@/components/site/testimonials.section'
import { Button } from '@chakra-ui/react'
import Link from 'next/link'

export default function Page() {
  return (
    <>
      <HeroSection />
      <CustomersSection />
      <ProductsSection />
      <ProductShowcaseSection />
      {/* <PricingSection /> */}
      <FounderSection />
      <TestimonialsSection />
      {/* <ContactSection /> */}
      <GetStartedSection>
        <Button variant="glass" colorPalette="accent" asChild>
          <Link href="/components">Browse components</Link>
        </Button>
        <Button variant="outline" asChild>
          <Link href="/pro/starter-kits">View starter kits</Link>
        </Button>
      </GetStartedSection>
    </>
  )
}
