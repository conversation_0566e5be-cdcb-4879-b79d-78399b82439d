{"name": "website", "version": "0.1.1-next.76", "private": true, "scripts": {"dev": "next dev --port 3020", "build": "next build", "start": "next start", "lint": "next lint", "types:ark": "pnpm tsx scripts/types/ark.ts", "types:recipe": "pnpm tsx scripts/types/recipe.ts", "types:component": "pnpm tsx scripts/types/component.ts", "generate:composition": "pnpm tsx scripts/composition.ts", "generate:types": "pnpm types:ark && pnpm types:recipe && pnpm types:component", "generate:icon": "pnpm tsx scripts/generate-icon.ts"}, "dependencies": {"@ark-ui/react": "^5.15.4", "@chakra-ui/charts": "^3.21.1", "@chakra-ui/react": "^3.21.1", "@emotion/react": "11.14.0", "@fumadocs/content-collections": "^1.2.0", "@octokit/rest": "^21.1.1", "@radix-ui/react-navigation-menu": "^1.2.9", "@react-three/drei": "^10.0.5", "@react-three/fiber": "^9.1.0", "@saas-ui/assets": "workspace:*", "@saas-ui/charts": "workspace:*", "@saas-ui/forms": "workspace:*", "@saas-ui/hooks": "workspace:*", "@saas-ui/modals": "workspace:*", "@saas-ui/react": "workspace:*", "@saas-ui/use-hotkeys": "workspace:*", "@shikijs/rehype": "3.6.0", "@shikijs/transformers": "3.6.0", "@tanstack/react-virtual": "^3.13.9", "hastscript": "9.0.1", "match-sorter": "8.0.0", "mdast": "3.0.0", "next": "^15.3.2", "next-themes": "0.4.6", "node-fetch": "3.3.2", "react": "19.0.0", "react-dom": "19.0.0", "react-icons": "5.5.0", "recharts": "^2.15.2", "rehype-autolink-headings": "7.1.0", "rehype-slug": "6.0.0", "remark-directive": "4.0.0", "remark-gfm": "4.0.1", "scule": "1.3.0", "shiki": "3.2.1", "three": "^0.174.0", "unist-util-visit": "5.0.0", "use-mask-input": "^3.4.2"}, "devDependencies": {"@content-collections/core": "^0.8.2", "@content-collections/mdx": "^0.2.2", "@content-collections/next": "^0.2.6", "@svgr/core": "8.1.0", "@types/mdast": "^4.0.4", "@types/node": "^22.13.13", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/three": "^0.174.0", "color2k": "2.0.3", "concurrently": "^9.1.2", "eslint": "^9.23.0", "eslint-config-next": "15.2.3", "fumadocs-core": "^15.3.0", "next-rspack": "^15.3.2", "typescript": "^5.8.2"}}