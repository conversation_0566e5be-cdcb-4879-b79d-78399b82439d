{"name": "@chakra-ui/compositions", "version": "1.0.1-next.40", "description": "Registry for component compositions", "scripts": {"typecheck": "tsc --noEmit"}, "keywords": ["chakra", "ui", "registry", "components", "compositions"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "ISC", "dependencies": {"@chakra-ui/react": "^3.20.0", "@hookform/resolvers": "4.1.3", "@saas-ui/forms": "workspace:*", "@saas-ui/react": "workspace:*", "@tanstack/react-table": "8.21.2", "next-themes": "0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-frame-component": "5.2.7", "react-hook-form": "7.54.2", "react-icons": "5.5.0", "react-lorem-ipsum": "1.4.10", "react-markdown": "10.1.0", "react-use": "17.6.0", "recharts": "2.15.1", "use-mask-input": "3.4.2", "zod": "3.24.2"}, "devDependencies": {"@types/react": "^19.0.12", "@types/react-dom": "^19.0.4"}}