'use client'

import { Checkbox, GridList, Tag, Text } from '@saas-ui/react'

export function GridListWithHeader() {
  return (
    <GridList.Root interactive>
      <GridList.Header>Users</GridList.Header>
      <GridList.Item>
        <GridList.Cell width="8">
          <Checkbox />
        </GridList.Cell>
        <GridList.Cell flex="1">
          <Text fontWeight="medium"><PERSON></Text>
          <Text textStyle="sm" color="fg.muted">
            <EMAIL>
          </Text>
        </GridList.Cell>
        <GridList.Cell>
          <Tag>Admin</Tag>
        </GridList.Cell>
      </GridList.Item>
      <GridList.Item>
        <GridList.Cell width="8">
          <Checkbox />
        </GridList.Cell>
        <GridList.Cell flex="1">
          <Text fontWeight="medium">Michael <PERSON></Text>
          <Text textStyle="sm" color="fg.muted">
            <EMAIL>
          </Text>
        </GridList.Cell>
        <GridList.Cell>
          <Tag>Member</Tag>
        </GridList.Cell>
      </GridList.Item>
      <GridList.Item>
        <GridList.Cell width="8">
          <Checkbox />
        </GridList.Cell>
        <GridList.Cell flex="1">
          <Text fontWeight="medium">Emma Rodriguez</Text>
          <Text textStyle="sm" color="fg.muted">
            <EMAIL>
          </Text>
        </GridList.Cell>
        <GridList.Cell>
          <Tag>Member</Tag>
        </GridList.Cell>
      </GridList.Item>
    </GridList.Root>
  )
}
