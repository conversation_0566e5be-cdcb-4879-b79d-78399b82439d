'use client'

import { Tabs } from '@saas-ui/react'
import { <PERSON><PERSON><PERSON><PERSON>, LuSquareCheck, Lu<PERSON>ser } from 'react-icons/lu'

export const TabsWithDisabledTab = () => {
  return (
    <Tabs.Root defaultValue="members">
      <Tabs.List>
        <Tabs.Trigger value="members">
          <LuUser />
          Members
        </Tabs.Trigger>
        <Tabs.Trigger value="projects" disabled>
          <LuFolder />
          Projects
        </Tabs.Trigger>
        <Tabs.Trigger value="tasks">
          <LuSquareCheck />
          Settings
        </Tabs.Trigger>
      </Tabs.List>
      {/* content */}
    </Tabs.Root>
  )
}
