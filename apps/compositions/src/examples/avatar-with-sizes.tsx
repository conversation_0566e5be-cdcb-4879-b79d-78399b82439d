import { HStack } from '@chakra-ui/react'
import { Avatar } from '@saas-ui/react'

export const AvatarWithSizes = () => {
  return (
    <HStack gap="3">
      <Avatar size="xs" name="<PERSON>" src="/avatars/1.png" />
      <Avatar size="sm" name="<PERSON>" src="/avatars/1.png" />
      <Avatar size="md" name="<PERSON>" src="/avatars/1.png" />
      <Avatar size="lg" name="<PERSON>" src="/avatars/1.png" />
      <Avatar size="xl" name="<PERSON>" src="/avatars/1.png" />
      <Avatar size="2xl" name="<PERSON>" src="/avatars/1.png" />
    </HStack>
  )
}
