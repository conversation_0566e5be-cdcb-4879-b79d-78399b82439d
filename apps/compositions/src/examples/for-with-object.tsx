import { Box, For, Stack, Text } from "@chakra-ui/react"

export const ForWithObject = () => {
  return (
    <Stack>
      <For
        each={[
          { name: "<PERSON><PERSON><PERSON>", powers: ["Shadow Clone", "<PERSON><PERSON><PERSON>"] },
          { name: "<PERSON><PERSON>", powers: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"] },
          { name: "<PERSON>", powers: ["Healing", "Super Strength"] },
        ]}
      >
        {(item, index) => (
          <Box borderWidth="1px" key={index} p="4">
            <Text fontWeight="bold">{item.name}</Text>
            <Text color="fg.muted">Powers: {item.powers.join(", ")}</Text>
          </Box>
        )}
      </For>
    </Stack>
  )
}
