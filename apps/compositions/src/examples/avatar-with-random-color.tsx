import { HStack } from '@chakra-ui/react'
import { Avatar } from '@saas-ui/react'

const colorPalette = ['red', 'blue', 'green', 'yellow', 'purple', 'orange']

const pickPalette = (name: string) => {
  const index = name.charCodeAt(0) % colorPalette.length
  return colorPalette[index]
}

export const AvatarWithRandomColor = () => {
  return (
    <HStack>
      <Avatar name="<PERSON>" colorPalette={pickPalette('<PERSON>')} />
      <Avatar name="<PERSON> Lesnar" colorPalette={pickPalette('Brook Lesnar')} />
      <Avatar name="<PERSON>" colorPalette={pickPalette('<PERSON>')} />
    </HStack>
  )
}
