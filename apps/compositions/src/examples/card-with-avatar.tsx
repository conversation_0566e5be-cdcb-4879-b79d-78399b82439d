'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Text } from '@chakra-ui/react'
import { Ava<PERSON>, Button, Card } from '@saas-ui/react'
import { <PERSON><PERSON><PERSON><PERSON>, LuX } from 'react-icons/lu'

export const CardWithAvatar = () => {
  return (
    <Card.Root width="320px">
      <Card.Body>
        <HStack mb="6" gap="3">
          <Avatar
            src="https://images.unsplash.com/photo-1511806754518-53bada35f930"
            name="<PERSON> Foss"
          />
          <Stack gap="0">
            <Text fontWeight="semibold" textStyle="sm">
              <PERSON>
            </Text>
            <Text color="fg.muted" textStyle="sm">
              @natefoss
            </Text>
          </Stack>
        </HStack>
        <Card.Description>
          <Strong color="fg"><PERSON> </Strong>
          has requested to join your team. You can approve or decline their
          request.
        </Card.Description>
      </Card.Body>
      <Card.Footer>
        <Button variant="subtle" colorPalette="red" flex="1">
          <LuX />
          Decline
        </Button>
        <Button variant="subtle" colorPalette="blue" flex="1">
          <LuCheck />
          Approve
        </Button>
      </Card.Footer>
    </Card.Root>
  )
}
