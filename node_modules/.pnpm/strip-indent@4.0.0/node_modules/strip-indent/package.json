{"name": "strip-indent", "version": "4.0.0", "description": "Strip leading whitespace from each line in a string", "license": "MIT", "repository": "sindresorhus/strip-indent", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["strip", "indent", "indentation", "normalize", "remove", "delete", "whitespace", "space", "tab", "string"], "dependencies": {"min-indent": "^1.0.1"}, "devDependencies": {"ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.39.1"}}