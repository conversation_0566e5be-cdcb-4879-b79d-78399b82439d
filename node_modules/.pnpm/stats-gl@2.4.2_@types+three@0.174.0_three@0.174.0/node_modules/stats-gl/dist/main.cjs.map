{"version": 3, "file": "main.cjs", "sources": ["../lib/main.ts"], "sourcesContent": null, "names": ["_Stats", "panel", "Panel"], "mappings": ";;AAgCA,MAAM,SAAN,MAAMA,QAAM;AAAA,EA0CV,YAAY;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,EACT,IAAkB,IAAI;AAxCtB,SAAQ,KAAoC;AAC5C,SAAQ,MAAkB;AAE1B,SAAQ,cAAiC;AACzC,SAAQ,aAA0B;AAClC,SAAQ,uBAAuB;AAK/B,SAAQ,SAAS;AACjB,SAAQ,cAAc;AACtB,SAAQ,wBAAwB;AAEhC,SAAQ,mBAAmB;AAC3B,SAAQ,mBAAmB;AAC3B,SAAQ,0BAA0B;AAClC,SAAQ,WAAW;AAInB,SAAQ,WAAyB;AACjC,SAAQ,kBAAgC;AAExC,SAAQ,aAA0B,EAAE,MAAM,CAAA,GAAI,OAAO,CAAA;AACrD,SAAQ,aAA0B,EAAE,MAAM,CAAA,GAAI,OAAO,CAAA;AACrD,SAAQ,aAA0B,EAAE,MAAM,CAAA,GAAI,OAAO,CAAA;AACrD,SAAQ,oBAAiC,EAAE,MAAM,CAAA,GAAI,OAAO,CAAA;AA4DpD,SAAA,cAAc,CAAC,UAA4B;AACjD,YAAM,eAAe;AACrB,WAAK,UAAU,EAAE,KAAK,OAAO,KAAK,IAAI,SAAS,MAAM;AAAA,IAAA;AAGvD,SAAQ,eAAe,MAAY;AAC5B,WAAA,YAAY,KAAK,UAAU,CAAC;AAC5B,WAAA,YAAY,KAAK,SAAS,CAAC;AAChC,UAAI,KAAK;AAAe,aAAA,YAAY,KAAK,UAAU,CAAC;AACpD,UAAI,KAAK;AAAsB,aAAA,YAAY,KAAK,iBAAiB,CAAC;AAAA,IAAA;AAvDlE,SAAK,OAAO;AACZ,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AAGhB,SAAA,MAAM,SAAS,cAAc,KAAK;AACvC,SAAK,cAAc;AAGd,SAAA,YAAY,YAAY;AAC7B,SAAK,WAAW,KAAK;AACrB,SAAK,cAAc,KAAK;AAGnB,SAAA,WAAW,KAAK,SAAS,IAAIA,QAAM,MAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;AAClE,SAAA,UAAU,KAAK,SAAS,IAAIA,QAAM,MAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;AAEtE,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EAGQ,gBAAsB;AACvB,SAAA,IAAI,MAAM,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMrB,KAAK,UAAU,qBAAqB,EAAE;AAAA;AAAA,EAE5C;AAAA,EAEQ,sBAA4B;AAClC,QAAI,KAAK,SAAS;AAChB,WAAK,IAAI,iBAAiB,SAAS,KAAK,WAAW;AAC9C,WAAA,UAAU,KAAK,IAAI;AAAA,IAAA,OACnB;AACE,aAAA,iBAAiB,UAAU,KAAK,YAAY;AAAA,IACrD;AAAA,EACF;AAAA,EAcA,MAAa,KACX,YACe;AACf,QAAI,CAAC,YAAY;AACf,cAAQ,MAAM,6CAA6C;AAC3D;AAAA,IACF;AAEI,QAAA,KAAK,oBAAoB,UAAU;AAAG;AACtC,QAAA,MAAM,KAAK,qBAAqB,UAAU;AAAG;AAC7C,QAAA,CAAC,KAAK,gBAAgB,UAAU;AAAG;AAAA,EAEzC;AAAA,EAEQ,oBAAoB,UAAwB;AAClD,QAAI,SAAS,mBAAmB,CAAC,KAAK,sBAAsB;AAC1D,WAAK,mBAAmB,QAAQ;AAC3B,WAAA,KAAK,SAAS;AAEnB,UAAI,KAAK,UAAU;AACjB,aAAK,sBAAsB;AAAA,MAC7B;AACO,aAAA;AAAA,IACT;AACO,WAAA;AAAA,EACT;AAAA,EAEA,MAAc,qBAAqB,UAAiC;AAClE,QAAI,SAAS,kBAAkB;AAC7B,UAAI,KAAK,UAAU;AACjB,iBAAS,QAAQ,iBAAiB;AAClC,YAAI,MAAM,SAAS,gBAAgB,iBAAiB,GAAG;AACrD,eAAK,uBAAuB;AAAA,QAC9B;AAAA,MACF;AACA,WAAK,OAAO,SAAS;AACd,aAAA;AAAA,IACT;AACO,WAAA;AAAA,EACT;AAAA,EAEQ,yBAA+B;AAChC,SAAA,WAAW,KAAK,SAAS,IAAIA,QAAM,MAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;AACvE,SAAK,kBAAkB,KAAK;AAAA,MAC1B,IAAIA,QAAM,MAAM,OAAO,WAAW,SAAS;AAAA,MAC3C;AAAA,IAAA;AAAA,EAEJ;AAAA,EAEQ,gBACN,YACS;AACT,QAAI,sBAAsB,wBAAwB;AAChD,WAAK,KAAK;AAAA,IAEV,WAAA,sBAAsB,qBACtB,sBAAsB,iBACtB;AACK,WAAA,KAAK,WAAW,WAAW,QAAQ;AACpC,UAAA,CAAC,KAAK,IAAI;AACZ,gBAAQ,MAAM,yCAAyC;AAChD,eAAA;AAAA,MACT;AAAA,IAAA,OACK;AACG,cAAA;AAAA,QACN;AAAA,MAAA;AAEK,aAAA;AAAA,IACT;AACO,WAAA;AAAA,EACT;AAAA,EAEQ,wBAA8B;AACpC,QAAI,KAAK,IAAI;AACX,WAAK,MAAM,KAAK,GAAG,aAAa,iCAAiC;AACjE,UAAI,KAAK,KAAK;AACP,aAAA,WAAW,KAAK,SAAS,IAAIA,QAAM,MAAM,OAAO,QAAQ,MAAM,GAAG,CAAC;AAAA,MACzE;AAAA,IACF;AAAA,EACF;AAAA,EAEO,QAAc;AACf,QAAA,CAAC,KAAK,uBAAuB;AAC/B,WAAK,eAAe,aAAa;AAAA,IACnC;AAEA,QAAI,CAAC,KAAK,MAAM,CAAC,KAAK;AAAK;AAE3B,QAAI,KAAK,aAAa;AACpB,WAAK,GAAG,SAAS,KAAK,IAAI,gBAAgB;AAAA,IAC5C;AAEK,SAAA,cAAc,KAAK,GAAG,YAAY;AACvC,QAAI,KAAK,aAAa;AACpB,WAAK,GAAG,WAAW,KAAK,IAAI,kBAAkB,KAAK,WAAW;AAAA,IAChE;AAAA,EACF;AAAA,EAEO,MAAY;AACZ,SAAA;AACL,QAAI,KAAK,MAAM,KAAK,OAAO,KAAK,aAAa;AAC3C,WAAK,GAAG,SAAS,KAAK,IAAI,gBAAgB;AAC1C,WAAK,WAAW,KAAK,EAAE,OAAO,KAAK,aAAa;AAChD,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EAEO,SAAe;AAChB,QAAA,CAAC,KAAK,MAAM;AACd,WAAK,kBAAkB;AAAA,IAAA,OAClB;AACL,WAAK,wBAAwB;AAAA,IAC/B;AAEK,SAAA,aAAa,eAAe,gBAAgB,cAAc;AAC/D,SAAK,eAAe;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA,EAEQ,0BAAgC;AACjC,SAAA,mBAAmB,KAAK,KAAM,OAAO;AACrC,SAAA,0BAA0B,KAAK,KAAM,QAAQ;AAClD,SAAK,aAAa,KAAK,yBAAyB,KAAK,iBAAiB;AAAA,EACxE;AAAA,EAEQ,iBAAuB;AAC7B,SAAK,aAAa,KAAK,kBAAkB,KAAK,UAAU;AACxD,SAAK,aAAa,KAAK,kBAAkB,KAAK,UAAU;AAAA,EAC1D;AAAA,EAEQ,gBAAsB;AAC5B,SAAK,cAAc;AACf,QAAA,KAAK,qBAAqB,GAAG;AAC/B,WAAK,eAAe,aAAa;AAAA,IACnC;AACA,SAAK,mBAAmB;AACxB,SAAK,WAAW;AACX,SAAA,YAAY,KAAK;EACxB;AAAA,EAGA,YAAYC,QAAc,QAAgB;AAElC,IAAAA,OAAA,OAAO,MAAM,WAAW;AAE9B,QAAI,KAAK,SAAS;AAEV,MAAAA,OAAA,OAAO,MAAM,UAAU;AAAA,IAAA,OAExB;AAEC,MAAAA,OAAA,OAAO,MAAM,UAAU;AAC7B,UAAI,KAAK,YAAY;AACb,QAAAA,OAAA,OAAO,MAAM,MAAM;AACzB,QAAAA,OAAM,OAAO,MAAM,OAAO,SAASA,OAAM,QAAQA,OAAM,KAAK;AAAA,MAAA,OACvD;AACC,QAAAA,OAAA,OAAO,MAAM,OAAO;AAC1B,QAAAA,OAAM,OAAO,MAAM,MAAM,SAASA,OAAM,SAASA,OAAM,KAAK;AAAA,MAE9D;AAAA,IACF;AAAA,EAEF;AAAA,EACA,SAASA,QAAc,QAAgB;AAErC,QAAIA,OAAM,QAAQ;AAEX,WAAA,IAAI,YAAYA,OAAM,MAAM;AAE5B,WAAA,YAAYA,QAAO,MAAM;AAAA,IAEhC;AAEO,WAAAA;AAAA,EAET;AAAA,EAEA,UAAU,IAAY;AAEpB,aAAS,IAAI,GAAG,IAAI,KAAK,IAAI,SAAS,QAAQ,KAAK;AACjD,YAAM,QAAQ,KAAK,IAAI,SAAS,CAAC;AAEjC,YAAM,MAAM,UAAU,MAAM,KAAK,UAAU;AAAA,IAE7C;AAEA,SAAK,OAAO;AAAA,EAEd;AAAA,EAEA,oBAAoB;AAGlB,QAAI,CAAC,KAAK,MAAM,CAAC,KAAK;AAAK;AAE3B,SAAK,mBAAmB;AAExB,SAAK,WAAW,QAAQ,CAAC,WAAW,UAAU;AAC5C,UAAI,KAAK,IAAI;AACL,cAAA,YAAY,KAAK,GAAG,kBAAkB,UAAU,OAAO,KAAK,GAAG,sBAAsB;AAC3F,cAAM,WAAW,KAAK,GAAG,aAAa,KAAK,IAAI,gBAAgB;AAE3D,YAAA,aAAa,CAAC,UAAU;AACpB,gBAAA,UAAU,KAAK,GAAG,kBAAkB,UAAU,OAAO,KAAK,GAAG,YAAY;AAC/E,gBAAM,WAAW,UAAU;AAC3B,eAAK,oBAAoB;AACpB,eAAA,GAAG,YAAY,UAAU,KAAK;AAC9B,eAAA,WAAW,OAAO,OAAO,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,IAAA,CACD;AAAA,EAEH;AAAA,EAEA,cAAc;AAEP,SAAA;AACC,UAAA,QAAQ,eAAe,MAAM,IAAI;AACjC,UAAA,UAAU,OAAO,KAAK;AAG5B,QAAI,QAAQ,KAAK,cAAc,MAAO,KAAK,eAAe;AAExD,YAAM,MAAM,KAAK,MAAO,KAAK,SAAS,MAAQ,OAAO;AAGhD,WAAA,aAAa,KAAK,KAAK,UAAU;AAGtC,WAAK,YAAY,KAAK,UAAU,KAAK,YAAY,CAAC;AAClD,WAAK,YAAY,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS;AAC9D,WAAK,YAAY,KAAK,UAAU,KAAK,YAAY,KAAK,SAAS;AAE/D,UAAI,KAAK,iBAAiB;AACxB,aAAK,YAAY,KAAK,iBAAiB,KAAK,iBAAiB;AAAA,MAC/D;AAGA,WAAK,SAAS;AACd,WAAK,cAAc;AACnB,WAAK,WAAW;AAAA,IAClB;AAEO,WAAA;AAAA,EAET;AAAA,EAEA,aAAa,OAAe,cAA0C;AAEvD,iBAAA,KAAK,KAAK,KAAK;AAC5B,QAAI,aAAa,KAAK,SAAS,KAAK,YAAY;AAE9C,mBAAa,KAAK;IAEpB;AAEa,iBAAA,MAAM,KAAK,KAAK;AAC7B,QAAI,aAAa,MAAM,SAAS,KAAK,cAAc;AAEjD,mBAAa,MAAM;IAErB;AAAA,EAEF;AAAA,EAEA,eAAe,QAAgB;AAE7B,QAAI,OAAO,aAAa;AAEf,aAAA,YAAY,KAAK,MAAM;AAC9B,WAAK,wBAAwB;AAAA,IAE/B;AAAA,EAEF;AAAA,EAEA,aAAa,aAA6D,WAA+B,aAAqB;AAE5H,QAAI,OAAO,eAAe,aAAa,KAAK,uBAAuB;AAE1D,aAAA,YAAY,KAAK,SAAS;AACjC,YAAM,aAAa,YAAY,QAAQ,aAAa,aAAa,SAAS;AAC1E,WAAK,oBAAoB,WAAW;AACpC,WAAK,wBAAwB;AAAA,IAE/B;AAAA,EAEF;AAAA,EAEA,YAAYA,QAAgC,cAAmD,YAAY,GAAG;AAExG,QAAA,aAAa,KAAK,SAAS,GAAG;AAEhC,UAAI,SAAS;AACb,UAAI,MAAM;AAEV,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK,QAAQ,KAAK;AAEvC,kBAAA,aAAa,KAAK,CAAC;AAE7B,YAAI,aAAa,KAAK,CAAC,IAAI,KAAK;AACxB,gBAAA,aAAa,KAAK,CAAC;AAAA,QAC3B;AAAA,MAEF;AAEA,UAAI,WAAW;AACf,UAAI,WAAW;AACf,eAAS,IAAI,GAAG,IAAI,aAAa,MAAM,QAAQ,KAAK;AAEtC,oBAAA,aAAa,MAAM,CAAC;AAEhC,YAAI,aAAa,MAAM,CAAC,IAAI,UAAU;AACzB,qBAAA,aAAa,MAAM,CAAC;AAAA,QACjC;AAAA,MAEF;AAEA,UAAIA,QAAO;AACH,QAAAA,OAAA,OAAO,SAAS,KAAK,IAAI,aAAa,KAAK,QAAQ,KAAK,UAAU,GAAG,WAAW,KAAK,IAAI,aAAa,MAAM,QAAQ,KAAK,YAAY,GAAG,KAAK,UAAU,SAAS;AAAA,MACxK;AAAA,IAEF;AAAA,EACF;AAAA,EAEA,IAAI,aAAa;AAEf,WAAO,KAAK;AAAA,EAEd;AAAA,EAEA,mBAAmB,UAAe;AAGhC,UAAM,uBAAuB,SAAS;AAGtC,UAAM,gBAAgB;AAGb,aAAA,SAAS,SAAU,OAAoB,QAAsB;AAGpE,oBAAc,MAAM;AAGC,2BAAA,KAAK,MAAM,OAAO,MAAM;AAE7C,oBAAc,IAAI;AAAA,IAAA;AAIpB,SAAK,uBAAuB;AAAA,EAE9B;AACF;AAjdM,OAwCG,QAAQC,MAAAA;AAxCjB,IAAM,QAAN;;"}