{"version": 3, "file": "style-to-object.js", "sources": ["../node_modules/inline-style-parser/index.js", "../src/index.ts"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", null], "names": [], "mappings": ";;;;;;;;;;CAAA;CACA;CACA,IAAI,aAAa,GAAG,iCAAiC,CAAC;AACtD;CACA,IAAI,aAAa,GAAG,KAAK,CAAC;CAC1B,IAAI,gBAAgB,GAAG,MAAM,CAAC;AAC9B;CACA;CACA,IAAI,cAAc,GAAG,wCAAwC,CAAC;CAC9D,IAAI,WAAW,GAAG,OAAO,CAAC;CAC1B,IAAI,WAAW,GAAG,sDAAsD,CAAC;CACzE,IAAI,eAAe,GAAG,SAAS,CAAC;AAChC;CACA;CACA,IAAI,UAAU,GAAG,YAAY,CAAC;AAC9B;CACA;CACA,IAAI,OAAO,GAAG,IAAI,CAAC;CACnB,IAAI,aAAa,GAAG,GAAG,CAAC;CACxB,IAAI,QAAQ,GAAG,GAAG,CAAC;CACnB,IAAI,YAAY,GAAG,EAAE,CAAC;AACtB;CACA;CACA,IAAI,YAAY,GAAG,SAAS,CAAC;CAC7B,IAAI,gBAAgB,GAAG,aAAa,CAAC;AACrC;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAA,iBAAc,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE;CAC3C,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACjC,IAAI,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;CAC3D,GAAG;AACH;CACA,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;AACxB;CACA,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC1B;CACA;CACA;CACA;CACA,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;CACjB,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,cAAc,CAAC,GAAG,EAAE;CAC/B,IAAI,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;CACzC,IAAI,IAAI,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;CACtC,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;CACrC,IAAI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;CACvD,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,QAAQ,GAAG;CACtB,IAAI,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CACjD,IAAI,OAAO,UAAU,IAAI,EAAE;CAC3B,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;CAC1C,MAAM,UAAU,EAAE,CAAC;CACnB,MAAM,OAAO,IAAI,CAAC;CAClB,KAAK,CAAC;CACN,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;CAC3B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACvB,IAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;CAChD,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;CACjC,GAAG;AACH;CACA;CACA;CACA;CACA,EAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK,CAAC;AAGrC;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,KAAK,CAAC,GAAG,EAAE;CACtB,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK;CACvB,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG;CAC/D,KAAK,CAAC;CACN,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC;CACrB,IAAI,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;CAClC,IAAI,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC;CACtB,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC;CACxB,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;AACvB;CACA,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE,CAEnB,MAAM;CACX,MAAM,MAAM,GAAG,CAAC;CAChB,KAAK;CACL,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,KAAK,CAAC,EAAE,EAAE;CACrB,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC3B,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO;CACnB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACnB,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;CACxB,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;CACpC,IAAI,OAAO,CAAC,CAAC;CACb,GAAG;AACH;CACA;CACA;CACA;CACA,EAAE,SAAS,UAAU,GAAG;CACxB,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;CAC5B,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;CAC3B,IAAI,IAAI,CAAC,CAAC;CACV,IAAI,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;CACxB,IAAI,QAAQ,CAAC,GAAG,OAAO,EAAE,GAAG;CAC5B,MAAM,IAAI,CAAC,KAAK,KAAK,EAAE;CACvB,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CACtB,OAAO;CACP,KAAK;CACL,IAAI,OAAO,KAAK,CAAC;CACjB,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,OAAO,GAAG;CACrB,IAAI,IAAI,GAAG,GAAG,QAAQ,EAAE,CAAC;CACzB,IAAI,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO;AAChF;CACA,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;CACd,IAAI;CACJ,MAAM,YAAY,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;CACrC,OAAO,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;CAC3E,MAAM;CACN,MAAM,EAAE,CAAC,CAAC;CACV,KAAK;CACL,IAAI,CAAC,IAAI,CAAC,CAAC;AACX;CACA,IAAI,IAAI,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;CAC9C,MAAM,OAAO,KAAK,CAAC,wBAAwB,CAAC,CAAC;CAC7C,KAAK;AACL;CACA,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CACpC,IAAI,MAAM,IAAI,CAAC,CAAC;CAChB,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;CACxB,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CAC3B,IAAI,MAAM,IAAI,CAAC,CAAC;AAChB;CACA,IAAI,OAAO,GAAG,CAAC;CACf,MAAM,IAAI,EAAE,YAAY;CACxB,MAAM,OAAO,EAAE,GAAG;CAClB,KAAK,CAAC,CAAC;CACP,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,WAAW,GAAG;CACzB,IAAI,IAAI,GAAG,GAAG,QAAQ,EAAE,CAAC;AACzB;CACA;CACA,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;CACrC,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO;CACtB,IAAI,OAAO,EAAE,CAAC;AACd;CACA;CACA,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,KAAK,CAAC,sBAAsB,CAAC,CAAC;AAClE;CACA;CACA,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;AACjC;CACA,IAAI,IAAI,GAAG,GAAG,GAAG,CAAC;CAClB,MAAM,IAAI,EAAE,gBAAgB;CAC5B,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;CAClE,MAAM,KAAK,EAAE,GAAG;CAChB,UAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;CAC3D,UAAU,YAAY;CACtB,KAAK,CAAC,CAAC;AACP;CACA;CACA,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AAC3B;CACA,IAAI,OAAO,GAAG,CAAC;CACf,GAAG;AACH;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,SAAS,YAAY,GAAG;CAC1B,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;AACnB;CACA,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpB;CACA;CACA,IAAI,IAAI,IAAI,CAAC;CACb,IAAI,QAAQ,IAAI,GAAG,WAAW,EAAE,GAAG;CACnC,MAAM,IAAI,IAAI,KAAK,KAAK,EAAE;CAC1B,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACzB,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC;CACxB,OAAO;CACP,KAAK;AACL;CACA,IAAI,OAAO,KAAK,CAAC;CACjB,GAAG;AACH;CACA,EAAE,UAAU,EAAE,CAAC;CACf,EAAE,OAAO,YAAY,EAAE,CAAC;CACxB,CAAC,CAAC;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA,SAAS,IAAI,CAAC,GAAG,EAAE;CACnB,EAAE,OAAO,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG,YAAY,CAAC;CACpE,CAAA;;;;CCrPA;;;;;;;;;;;;;CAaG;CACW,SAAU,aAAa,CACnC,KAAa,EACb,QAAmB,EAAA;KAEnB,IAAI,WAAW,GAAuB,IAAI,CAAC;KAE3C,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;CACvC,QAAA,OAAO,WAAW,CAAC;MACpB;CAED,IAAA,IAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;CAClC,IAAA,IAAM,WAAW,GAAG,OAAO,QAAQ,KAAK,UAAU,CAAC;CAEnD,IAAA,YAAY,CAAC,OAAO,CAAC,UAAC,WAAW,EAAA;CAC/B,QAAA,IAAI,WAAW,CAAC,IAAI,KAAK,aAAa,EAAE;aACtC,OAAO;UACR;SAEO,IAAA,QAAQ,GAAY,WAAW,CAAA,QAAvB,EAAE,KAAK,GAAK,WAAW,CAAA,KAAhB,CAAiB;SAExC,IAAI,WAAW,EAAE;CACf,YAAA,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;UACxC;cAAM,IAAI,KAAK,EAAE;CAChB,YAAA,WAAW,GAAG,WAAW,IAAI,EAAE,CAAC;CAChC,YAAA,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC;UAC/B;CACH,KAAC,CAAC,CAAC;CAEH,IAAA,OAAO,WAAW,CAAC;CACrB;;;;;;;;", "x_google_ignoreList": [0]}