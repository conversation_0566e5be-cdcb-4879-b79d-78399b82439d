{"name": "stats.js", "version": "0.17.0", "description": "JavaScript Performance Monitor", "main": "build/stats.min.js", "directories": {"example": "examples"}, "files": ["build", "src"], "scripts": {"build": "rollup -c", "build-uglify": "rollup -c && uglifyjs build/stats.js -cm --preamble \"// stats.js - http://github.com/mrdoob/stats.js\" > build/stats.min.js", "build-closure": "rollup -c && java -jar utils/compiler/closure-compiler-v20160713.jar --language_in=ECMASCRIPT5_STRICT --js build/stats.js --js_output_file build/stats.min.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/mrdoob/stats.js.git"}, "keywords": ["performance", "fps", "stats"], "author": "m<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mrdoob/stats.js/issues"}, "homepage": "https://github.com/mrdoob/stats.js", "devDependencies": {"rollup": "^0.36.0", "uglifyjs": "^2.4.10"}}