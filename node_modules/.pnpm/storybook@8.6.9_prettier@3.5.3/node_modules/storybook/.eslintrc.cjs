
module.exports = {
  overrides: [
    {
      files: 'templates/**/*',
      env: {
        browser: true,
      },
      rules: {
        'react/no-this-in-sfc': "off",
        'react/react-in-jsx-scope': "off",
        'global-require': "off",
        'no-redeclare': "off",
        'react/prop-types': "off",
      },
    },
    {
      files: 'rendererAssets/**/*',
      env: {
        browser: true,
      },
      rules: {
        'jsx-a11y/anchor-is-valid': "off",
        'react/prop-types': "off",
        'react/react-in-jsx-scope': "off",
        'import/extensions': "off",
        'import/named': "off",
      },
    },
  ],
};
