import ESM_COMPAT_Module1 from 'node:module';
import 'node:url';
import 'node:path';

const require = ESM_COMPAT_Module1.createRequire(import.meta.url);
var __require=(x=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(x,{get:(a,b)=>(typeof require<"u"?require:a)[b]}):x)(function(x){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+x+'" is not supported')});

export { __require };
