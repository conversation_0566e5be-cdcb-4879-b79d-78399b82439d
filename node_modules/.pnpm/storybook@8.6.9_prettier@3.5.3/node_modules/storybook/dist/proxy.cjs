"use strict";var import_common=require("@storybook/core/common"),import_child_process=require("child_process"),args=process.argv.slice(2);if(["dev","build"].includes(args[0]))require("@storybook/core/cli/bin");else{let command=["npx","--yes",...args[0]==="init"?[`create-storybook@${import_common.versions.storybook}`,...args.slice(1)]:[`@storybook/cli@${import_common.versions.storybook}`,...args]];(0,import_child_process.spawn)(command[0],command.slice(1),{stdio:"inherit",shell:!0}).on("exit",code=>{code!=null&&process.exit(code),process.exit(1)})}
