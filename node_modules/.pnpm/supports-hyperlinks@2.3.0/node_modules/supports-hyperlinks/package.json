{"name": "supports-hyperlinks", "version": "2.3.0", "description": "Detect if your terminal emulator supports hyperlinks", "license": "MIT", "repository": "jamestalmage/supports-hyperlinks", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=8"}, "scripts": {"prepublishOnly": "npm run create-types", "test": "xo && nyc ava", "create-types": "tsc index.js --allowJs  --declaration --emitDeclarationOnly"}, "files": ["index.js", "browser.js"], "browser": "browser.js", "keywords": ["link", "terminal", "hyperlink", "cli"], "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "devDependencies": {"ava": "^2.2.0", "codecov": "^3.5.0", "nyc": "^14.1.1", "typescript": "^3.7.2", "xo": "^0.24.0"}, "nyc": {"reporter": ["lcov", "text"]}}