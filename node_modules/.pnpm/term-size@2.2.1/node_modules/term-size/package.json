{"name": "term-size", "version": "2.2.1", "description": "Reliably get the terminal window size (columns & rows)", "license": "MIT", "repository": "sindresorhus/term-size", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "vendor"], "keywords": ["terminal", "size", "console", "window", "width", "height", "columns", "rows", "lines", "tty", "redirected"], "devDependencies": {"ava": "^2.4.0", "execa": "^3.4.0", "tsd": "^0.11.0", "xo": "^0.25.3"}}