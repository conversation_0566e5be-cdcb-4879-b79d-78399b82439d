{"name": "storybook", "version": "8.6.14", "description": "Storybook's CLI - install, dev, build, upgrade, and more", "keywords": ["cli", "generator", "dev", "build", "upgrade"], "homepage": "https://github.com/storybookjs/storybook/tree/next/code/lib/cli", "bugs": {"url": "https://github.com/storybookjs/storybook/issues"}, "repository": {"type": "git", "url": "https://github.com/storybookjs/storybook.git", "directory": "code/lib/cli"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/storybook"}, "license": "MIT", "author": "Storybook Team", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "node": "./dist/index.cjs", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./bin/index.cjs": "./bin/index.cjs", "./core-path": {"types": "./dist/core-path.d.ts", "import": "./dist/core-path.js", "require": "./dist/core-path.cjs"}, "./package.json": "./package.json", "./core": {"types": "./core/index.d.ts", "import": "./core/index.js", "require": "./core/index.cjs"}, "./internal/node-logger": {"types": "./core/node-logger/index.d.ts", "import": "./core/node-logger/index.js", "require": "./core/node-logger/index.cjs"}, "./internal/client-logger": {"types": "./core/client-logger/index.d.ts", "import": "./core/client-logger/index.js", "require": "./core/client-logger/index.cjs"}, "./internal/core-server": {"types": "./core/core-server/index.d.ts", "import": "./core/core-server/index.js", "require": "./core/core-server/index.cjs"}, "./internal/core-server/presets/common-preset": {"import": "./core/core-server/presets/common-preset.js", "require": "./core/core-server/presets/common-preset.cjs"}, "./internal/core-server/presets/common-manager": {"import": "./core/core-server/presets/common-manager.js"}, "./internal/core-server/presets/common-override-preset": {"import": "./core/core-server/presets/common-override-preset.js", "require": "./core/core-server/presets/common-override-preset.cjs"}, "./internal/core-events": {"types": "./core/core-events/index.d.ts", "import": "./core/core-events/index.js", "require": "./core/core-events/index.cjs"}, "./internal/manager-errors": {"types": "./core/manager-errors.d.ts", "import": "./core/manager-errors.js"}, "./internal/preview-errors": {"types": "./core/preview-errors.d.ts", "import": "./core/preview-errors.js", "require": "./core/preview-errors.cjs"}, "./internal/server-errors": {"types": "./core/server-errors.d.ts", "import": "./core/server-errors.js", "require": "./core/server-errors.cjs"}, "./internal/channels": {"types": "./core/channels/index.d.ts", "import": "./core/channels/index.js", "require": "./core/channels/index.cjs"}, "./internal/types": {"types": "./core/types/index.d.ts", "import": "./core/types/index.js", "require": "./core/types/index.cjs"}, "./internal/csf": {"types": "./core/csf/index.d.ts", "import": "./core/csf/index.js", "require": "./core/csf/index.cjs"}, "./internal/csf-tools": {"types": "./core/csf-tools/index.d.ts", "import": "./core/csf-tools/index.js", "require": "./core/csf-tools/index.cjs"}, "./internal/common": {"types": "./core/common/index.d.ts", "import": "./core/common/index.js", "require": "./core/common/index.cjs"}, "./internal/builder-manager": {"types": "./core/builder-manager/index.d.ts", "import": "./core/builder-manager/index.js", "require": "./core/builder-manager/index.cjs"}, "./internal/telemetry": {"types": "./core/telemetry/index.d.ts", "import": "./core/telemetry/index.js", "require": "./core/telemetry/index.cjs"}, "./internal/preview-api": {"types": "./core/preview-api/index.d.ts", "import": "./core/preview-api/index.js", "require": "./core/preview-api/index.cjs"}, "./internal/manager-api": {"types": "./core/manager-api/index.d.ts", "import": "./core/manager-api/index.js", "require": "./core/manager-api/index.cjs"}, "./internal/router": {"types": "./core/router/index.d.ts", "import": "./core/router/index.js", "require": "./core/router/index.cjs"}, "./internal/components": {"types": "./core/components/index.d.ts", "import": "./core/components/index.js", "require": "./core/components/index.cjs"}, "./internal/theming": {"types": "./core/theming/index.d.ts", "import": "./core/theming/index.js", "require": "./core/theming/index.cjs"}, "./internal/theming/create": {"types": "./core/theming/create.d.ts", "import": "./core/theming/create.js", "require": "./core/theming/create.cjs"}, "./internal/docs-tools": {"types": "./core/docs-tools/index.d.ts", "import": "./core/docs-tools/index.js", "require": "./core/docs-tools/index.cjs"}, "./internal/manager/globals-module-info": {"types": "./core/manager/globals-module-info.d.ts", "import": "./core/manager/globals-module-info.js", "require": "./core/manager/globals-module-info.cjs"}, "./internal/preview/globals": {"types": "./core/preview/globals.d.ts", "import": "./core/preview/globals.js", "require": "./core/preview/globals.cjs"}, "./internal/cli": {"types": "./core/cli/index.d.ts", "import": "./core/cli/index.js", "require": "./core/cli/index.cjs"}, "./internal/cli/bin": {"types": "./core/cli/bin/index.d.ts", "import": "./core/cli/bin/index.js", "require": "./core/cli/bin/index.cjs"}, "./internal/manager/globals": {"types": "./core/manager/globals.d.ts", "import": "./core/manager/globals.js", "require": "./core/manager/globals.cjs"}, "./internal/babel": {"types": "./core/babel/index.d.ts", "import": "./core/babel/index.js", "require": "./core/babel/index.cjs"}, "./internal/manager/globals-runtime": {"import": "./core/manager/globals-runtime.js"}, "./internal/preview/runtime": {"import": "./core/preview/runtime.js"}}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "typesVersions": {"*": {"internal/*": ["./core/index.d.ts"], "internal/babel": ["./core/babel/index.d.ts"], "internal/builder-manager": ["./core/builder-manager/index.d.ts"], "internal/channels": ["./core/channels/index.d.ts"], "internal/cli": ["./core/cli/index.d.ts"], "internal/cli/bin": ["./core/cli/bin/index.d.ts"], "internal/client-logger": ["./core/client-logger/index.d.ts"], "internal/common": ["./core/common/index.d.ts"], "internal/components": ["./core/components/index.d.ts"], "internal/core-events": ["./core/core-events/index.d.ts"], "internal/core-server": ["./core/core-server/index.d.ts"], "internal/csf": ["./core/csf/index.d.ts"], "internal/csf-tools": ["./core/csf-tools/index.d.ts"], "internal/docs-tools": ["./core/docs-tools/index.d.ts"], "internal/manager-api": ["./core/manager-api/index.d.ts"], "internal/manager-errors": ["./core/manager-errors.d.ts"], "internal/manager/globals": ["./core/manager/globals.d.ts"], "internal/manager/globals-module-info": ["./core/manager/globals-module-info.d.ts"], "internal/node-logger": ["./core/node-logger/index.d.ts"], "internal/preview-api": ["./core/preview-api/index.d.ts"], "internal/preview-errors": ["./core/preview-errors.d.ts"], "internal/preview/globals": ["./core/preview/globals.d.ts"], "internal/router": ["./core/router/index.d.ts"], "internal/server-errors": ["./core/server-errors.d.ts"], "internal/telemetry": ["./core/telemetry/index.d.ts"], "internal/theming": ["./core/theming/index.d.ts"], "internal/theming/create": ["./core/theming/create.d.ts"], "internal/types": ["./core/types/index.d.ts"], "*": ["./dist/index.d.ts"], "core-path": ["./dist/core-path.d.ts"], "core": ["./core/index.d.ts"]}}, "bin": {"getstorybook": "./bin/index.cjs", "sb": "./bin/index.cjs", "storybook": "./bin/index.cjs"}, "files": ["bin/**/*", "core/**/*", "dist/**/*", "rendererAssets/**/*", "templates/**/*", "README.md", "*.cjs", "*.js", "*.d.ts", "!src/**/*"], "scripts": {"check": "jiti ../../../scripts/prepare/check.ts", "prep": "jiti ../../../scripts/prepare/bundle.ts", "sb": "node ./bin/index.js"}, "dependencies": {"@storybook/core": "8.6.14"}, "devDependencies": {"typescript": "^5.7.3"}, "peerDependencies": {"prettier": "^2 || ^3"}, "peerDependenciesMeta": {"prettier": {"optional": true}}, "publishConfig": {"access": "public"}, "bundler": {"pre": "./scripts/update-core-portal.ts", "entries": ["./src/proxy.ts", "./src/core-path.ts", "./src/index.ts"], "formats": ["cjs", "node-esm"]}, "gitHead": "e6a7fd8a655c69780bc20b9749c2699e44beae16"}