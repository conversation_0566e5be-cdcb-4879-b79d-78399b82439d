!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):e.ES6Promise=n()}(this,function(){"use strict";function e(e){return"function"==typeof e}var n=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},t=0,r=void 0,o=void 0,i=function(e,n){p[t]=e,p[t+1]=n,2===(t+=2)&&(o?o(g):v())};var s="undefined"!=typeof window?window:void 0,a=s||{},u=a.MutationObserver||a.WebKitMutationObserver,c="undefined"==typeof self&&"undefined"!=typeof process&&"[object process]"==={}.toString.call(process),l="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function f(){var e=setTimeout;return function(){return e(g,1)}}var p=new Array(1e3);function g(){for(var e=0;e<t;e+=2){(0,p[e])(p[e+1]),p[e]=void 0,p[e+1]=void 0}t=0}var h,m,d,_,v=void 0;function y(e,n){var t=arguments,r=this,o=new this.constructor(C);void 0===o[w]&&U(o);var s,a=r._state;return a?(s=t[a-1],i(function(){return B(a,o,s,r._result)})):T(r,o,e,n),o}function b(e){if(e&&"object"==typeof e&&e.constructor===this)return e;var n=new this(C);return N(n,e),n}c?v=function(){return process.nextTick(g)}:u?(m=0,d=new u(g),_=document.createTextNode(""),d.observe(_,{characterData:!0}),v=function(){_.data=m=++m%2}):l?((h=new MessageChannel).port1.onmessage=g,v=function(){return h.port2.postMessage(0)}):v=void 0===s&&"function"==typeof require?function(){try{var e=require("vertx");return r=e.runOnLoop||e.runOnContext,function(){r(g)}}catch(e){return f()}}():f();var w=Math.random().toString(36).substring(16);function C(){}var A=void 0,O=1,E=2,M=new F;function S(e){try{return e.then}catch(e){return M.error=e,M}}function L(n,t,r){t.constructor===n.constructor&&r===y&&t.constructor.resolve===b?function(e,n){n._state===O?R(e,n._result):n._state===E?j(e,n._result):T(n,void 0,function(n){return N(e,n)},function(n){return j(e,n)})}(n,t):r===M?j(n,M.error):void 0===r?R(n,t):e(r)?function(e,n,t){i(function(e){var r=!1,o=function(e,n,t,r){try{e.call(n,t,r)}catch(e){return e}}(t,n,function(t){r||(r=!0,n!==t?N(e,t):R(e,t))},function(n){r||(r=!0,j(e,n))},e._label);!r&&o&&(r=!0,j(e,o))},e)}(n,t,r):R(n,t)}function N(e,n){var t;e===n?j(e,new TypeError("You cannot resolve a promise with itself")):"function"==typeof(t=n)||"object"==typeof t&&null!==t?L(e,n,S(n)):R(e,n)}function P(e){e._onerror&&e._onerror(e._result),x(e)}function R(e,n){e._state===A&&(e._result=n,e._state=O,0!==e._subscribers.length&&i(x,e))}function j(e,n){e._state===A&&(e._state=E,e._result=n,i(P,e))}function T(e,n,t,r){var o=e._subscribers,s=o.length;e._onerror=null,o[s]=n,o[s+O]=t,o[s+E]=r,0===s&&e._state&&i(x,e)}function x(e){var n=e._subscribers,t=e._state;if(0!==n.length){for(var r=void 0,o=void 0,i=e._result,s=0;s<n.length;s+=3)r=n[s],o=n[s+t],r?B(t,r,o,i):o(i);e._subscribers.length=0}}function F(){this.error=null}var G=new F;function B(n,t,r,o){var i=e(r),s=void 0,a=void 0,u=void 0,c=void 0;if(i){if((s=function(e,n){try{return e(n)}catch(e){return G.error=e,G}}(r,o))===G?(c=!0,a=s.error,s=null):u=!0,t===s)return void j(t,new TypeError("A promises callback cannot return that same promise."))}else s=o,u=!0;t._state!==A||(i&&u?N(t,s):c?j(t,a):n===O?R(t,s):n===E&&j(t,s))}var D=0;function U(e){e[w]=D++,e._state=void 0,e._result=void 0,e._subscribers=[]}function k(e,t){this._instanceConstructor=e,this.promise=new e(C),this.promise[w]||U(this.promise),n(t)?(this._input=t,this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0===this.length?R(this.promise,this._result):(this.length=this.length||0,this._enumerate(),0===this._remaining&&R(this.promise,this._result))):j(this.promise,new Error("Array Methods must be provided an Array"))}function I(e){this[w]=D++,this._result=this._state=void 0,this._subscribers=[],C!==e&&("function"!=typeof e&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof I?function(e,n){try{n(function(n){N(e,n)},function(n){j(e,n)})}catch(n){j(e,n)}}(this,e):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}function $(){var e=void 0;if("undefined"!=typeof global)e=global;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var n=e.Promise;if(n){var t=null;try{t=Object.prototype.toString.call(n.resolve())}catch(e){}if("[object Promise]"===t&&!n.cast)return}e.Promise=I}return k.prototype._enumerate=function(){for(var e=this.length,n=this._input,t=0;this._state===A&&t<e;t++)this._eachEntry(n[t],t)},k.prototype._eachEntry=function(e,n){var t=this._instanceConstructor,r=t.resolve;if(r===b){var o=S(e);if(o===y&&e._state!==A)this._settledAt(e._state,n,e._result);else if("function"!=typeof o)this._remaining--,this._result[n]=e;else if(t===I){var i=new t(C);L(i,e,o),this._willSettleAt(i,n)}else this._willSettleAt(new t(function(n){return n(e)}),n)}else this._willSettleAt(r(e),n)},k.prototype._settledAt=function(e,n,t){var r=this.promise;r._state===A&&(this._remaining--,e===E?j(r,t):this._result[n]=t),0===this._remaining&&R(r,this._result)},k.prototype._willSettleAt=function(e,n){var t=this;T(e,void 0,function(e){return t._settledAt(O,n,e)},function(e){return t._settledAt(E,n,e)})},I.all=function(e){return new k(this,e).promise},I.race=function(e){var t=this;return n(e)?new t(function(n,r){for(var o=e.length,i=0;i<o;i++)t.resolve(e[i]).then(n,r)}):new t(function(e,n){return n(new TypeError("You must pass an array to race."))})},I.resolve=b,I.reject=function(e){var n=new this(C);return j(n,e),n},I._setScheduler=function(e){o=e},I._setAsap=function(e){i=e},I._asap=i,I.prototype={constructor:I,then:y,catch:function(e){return this.then(null,e)}},$(),I.polyfill=$,I.Promise=I,I}),"undefined"==typeof Promise&&ES6Promise.polyfill(),function(e,n){"use strict";"function"==typeof define&&define.amd?define("stackframe",[],n):"object"==typeof exports?module.exports=n():e.StackFrame=n()}(this,function(){"use strict";function e(e){return e.charAt(0).toUpperCase()+e.substring(1)}function n(e){return function(){return this[e]}}var t=["isConstructor","isEval","isNative","isToplevel"],r=["columnNumber","lineNumber"],o=["fileName","functionName","source"],i=t.concat(r,o,["args"],["evalOrigin"]);function s(n){if(n)for(var t=0;t<i.length;t++)void 0!==n[i[t]]&&this["set"+e(i[t])](n[i[t]])}s.prototype={getArgs:function(){return this.args},setArgs:function(e){if("[object Array]"!==Object.prototype.toString.call(e))throw new TypeError("Args must be an Array");this.args=e},getEvalOrigin:function(){return this.evalOrigin},setEvalOrigin:function(e){if(e instanceof s)this.evalOrigin=e;else{if(!(e instanceof Object))throw new TypeError("Eval Origin must be an Object or StackFrame");this.evalOrigin=new s(e)}},toString:function(){var e=this.getFileName()||"",n=this.getLineNumber()||"",t=this.getColumnNumber()||"",r=this.getFunctionName()||"";return this.getIsEval()?e?"[eval] ("+e+":"+n+":"+t+")":"[eval]:"+n+":"+t:r?r+" ("+e+":"+n+":"+t+")":e+":"+n+":"+t}},s.fromString=function(e){var n=e.indexOf("("),t=e.lastIndexOf(")"),r=e.substring(0,n),o=e.substring(n+1,t).split(","),i=e.substring(t+1);if(0===i.indexOf("@"))var a=/@(.+?)(?::(\d+))?(?::(\d+))?$/.exec(i,""),u=a[1],c=a[2],l=a[3];return new s({functionName:r,args:o||void 0,fileName:u,lineNumber:c||void 0,columnNumber:l||void 0})};for(var a=0;a<t.length;a++)s.prototype["get"+e(t[a])]=n(t[a]),s.prototype["set"+e(t[a])]=function(e){return function(n){this[e]=Boolean(n)}}(t[a]);for(var u=0;u<r.length;u++)s.prototype["get"+e(r[u])]=n(r[u]),s.prototype["set"+e(r[u])]=function(e){return function(n){if(t=n,isNaN(parseFloat(t))||!isFinite(t))throw new TypeError(e+" must be a Number");var t;this[e]=Number(n)}}(r[u]);for(var c=0;c<o.length;c++)s.prototype["get"+e(o[c])]=n(o[c]),s.prototype["set"+e(o[c])]=function(e){return function(n){this[e]=String(n)}}(o[c]);return s});var SourceMap=function(e){var n={};function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:r})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,n){if(1&n&&(e=t(e)),8&n)return e;if(4&n&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(t.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var o in e)t.d(r,o,function(n){return e[n]}.bind(null,o));return r},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},t.p="",t(t.s=1)}([function(e,n){n.getArg=function(e,n,t){if(n in e)return e[n];if(3===arguments.length)return t;throw new Error('"'+n+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.]*)(?::(\d+))?(\S*)$/,r=/^data:.+\,.+$/;function o(e){var n=e.match(t);return n?{scheme:n[1],auth:n[2],host:n[3],port:n[4],path:n[5]}:null}function i(e){var n="";return e.scheme&&(n+=e.scheme+":"),n+="//",e.auth&&(n+=e.auth+"@"),e.host&&(n+=e.host),e.port&&(n+=":"+e.port),e.path&&(n+=e.path),n}function s(e){var t=e,r=o(e);if(r){if(!r.path)return e;t=r.path}for(var s,a=n.isAbsolute(t),u=t.split(/\/+/),c=0,l=u.length-1;l>=0;l--)"."===(s=u[l])?u.splice(l,1):".."===s?c++:c>0&&(""===s?(u.splice(l+1,c),c=0):(u.splice(l,2),c--));return""===(t=u.join("/"))&&(t=a?"/":"."),r?(r.path=t,i(r)):t}n.urlParse=o,n.urlGenerate=i,n.normalize=s,n.join=function(e,n){""===e&&(e="."),""===n&&(n=".");var t=o(n),a=o(e);if(a&&(e=a.path||"/"),t&&!t.scheme)return a&&(t.scheme=a.scheme),i(t);if(t||n.match(r))return n;if(a&&!a.host&&!a.path)return a.host=n,i(a);var u="/"===n.charAt(0)?n:s(e.replace(/\/+$/,"")+"/"+n);return a?(a.path=u,i(a)):u},n.isAbsolute=function(e){return"/"===e.charAt(0)||!!e.match(t)},n.relative=function(e,n){""===e&&(e="."),e=e.replace(/\/$/,"");for(var t=0;0!==n.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0)return n;if((e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return n;++t}return Array(t+1).join("../")+n.substr(e.length+1)};var a=!("__proto__"in Object.create(null));function u(e){return e}function c(e){if(!e)return!1;var n=e.length;if(n<9)return!1;if(95!==e.charCodeAt(n-1)||95!==e.charCodeAt(n-2)||111!==e.charCodeAt(n-3)||116!==e.charCodeAt(n-4)||111!==e.charCodeAt(n-5)||114!==e.charCodeAt(n-6)||112!==e.charCodeAt(n-7)||95!==e.charCodeAt(n-8)||95!==e.charCodeAt(n-9))return!1;for(var t=n-10;t>=0;t--)if(36!==e.charCodeAt(t))return!1;return!0}function l(e,n){return e===n?0:e>n?1:-1}n.toSetString=a?u:function(e){return c(e)?"$"+e:e},n.fromSetString=a?u:function(e){return c(e)?e.slice(1):e},n.compareByOriginalPositions=function(e,n,t){var r=e.source-n.source;return 0!==r?r:0!=(r=e.originalLine-n.originalLine)?r:0!=(r=e.originalColumn-n.originalColumn)||t?r:0!=(r=e.generatedColumn-n.generatedColumn)?r:0!=(r=e.generatedLine-n.generatedLine)?r:e.name-n.name},n.compareByGeneratedPositionsDeflated=function(e,n,t){var r=e.generatedLine-n.generatedLine;return 0!==r?r:0!=(r=e.generatedColumn-n.generatedColumn)||t?r:0!=(r=e.source-n.source)?r:0!=(r=e.originalLine-n.originalLine)?r:0!=(r=e.originalColumn-n.originalColumn)?r:e.name-n.name},n.compareByGeneratedPositionsInflated=function(e,n){var t=e.generatedLine-n.generatedLine;return 0!==t?t:0!=(t=e.generatedColumn-n.generatedColumn)?t:0!==(t=l(e.source,n.source))?t:0!=(t=e.originalLine-n.originalLine)?t:0!=(t=e.originalColumn-n.originalColumn)?t:l(e.name,n.name)}},function(e,n,t){var r=t(0),o=t(2),i=t(3).ArraySet,s=t(4),a=t(6).quickSort;function u(e){var n=e;return"string"==typeof e&&(n=JSON.parse(e.replace(/^\)\]\}'/,""))),null!=n.sections?new f(n):new c(n)}function c(e){var n=e;"string"==typeof e&&(n=JSON.parse(e.replace(/^\)\]\}'/,"")));var t=r.getArg(n,"version"),o=r.getArg(n,"sources"),s=r.getArg(n,"names",[]),a=r.getArg(n,"sourceRoot",null),u=r.getArg(n,"sourcesContent",null),c=r.getArg(n,"mappings"),l=r.getArg(n,"file",null);if(t!=this._version)throw new Error("Unsupported version: "+t);o=o.map(String).map(r.normalize).map(function(e){return a&&r.isAbsolute(a)&&r.isAbsolute(e)?r.relative(a,e):e}),this._names=i.fromArray(s.map(String),!0),this._sources=i.fromArray(o,!0),this.sourceRoot=a,this.sourcesContent=u,this._mappings=c,this.file=l}function l(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function f(e){var n=e;"string"==typeof e&&(n=JSON.parse(e.replace(/^\)\]\}'/,"")));var t=r.getArg(n,"version"),o=r.getArg(n,"sections");if(t!=this._version)throw new Error("Unsupported version: "+t);this._sources=new i,this._names=new i;var s={line:-1,column:0};this._sections=o.map(function(e){if(e.url)throw new Error("Support for url field in sections not implemented.");var n=r.getArg(e,"offset"),t=r.getArg(n,"line"),o=r.getArg(n,"column");if(t<s.line||t===s.line&&o<s.column)throw new Error("Section offsets must be ordered and non-overlapping.");return s=n,{generatedOffset:{generatedLine:t+1,generatedColumn:o+1},consumer:new u(r.getArg(e,"map"))}})}u.fromSourceMap=function(e){return c.fromSourceMap(e)},u.prototype._version=3,u.prototype.__generatedMappings=null,Object.defineProperty(u.prototype,"_generatedMappings",{get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),u.prototype.__originalMappings=null,Object.defineProperty(u.prototype,"_originalMappings",{get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),u.prototype._charIsMappingSeparator=function(e,n){var t=e.charAt(n);return";"===t||","===t},u.prototype._parseMappings=function(e,n){throw new Error("Subclasses must implement _parseMappings")},u.GENERATED_ORDER=1,u.ORIGINAL_ORDER=2,u.GREATEST_LOWER_BOUND=1,u.LEAST_UPPER_BOUND=2,u.prototype.eachMapping=function(e,n,t){var o,i=n||null;switch(t||u.GENERATED_ORDER){case u.GENERATED_ORDER:o=this._generatedMappings;break;case u.ORIGINAL_ORDER:o=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var s=this.sourceRoot;o.map(function(e){var n=null===e.source?null:this._sources.at(e.source);return null!=n&&null!=s&&(n=r.join(s,n)),{source:n,generatedLine:e.generatedLine,generatedColumn:e.generatedColumn,originalLine:e.originalLine,originalColumn:e.originalColumn,name:null===e.name?null:this._names.at(e.name)}},this).forEach(e,i)},u.prototype.allGeneratedPositionsFor=function(e){var n=r.getArg(e,"line"),t={source:r.getArg(e,"source"),originalLine:n,originalColumn:r.getArg(e,"column",0)};if(null!=this.sourceRoot&&(t.source=r.relative(this.sourceRoot,t.source)),!this._sources.has(t.source))return[];t.source=this._sources.indexOf(t.source);var i=[],s=this._findMapping(t,this._originalMappings,"originalLine","originalColumn",r.compareByOriginalPositions,o.LEAST_UPPER_BOUND);if(s>=0){var a=this._originalMappings[s];if(void 0===e.column)for(var u=a.originalLine;a&&a.originalLine===u;)i.push({line:r.getArg(a,"generatedLine",null),column:r.getArg(a,"generatedColumn",null),lastColumn:r.getArg(a,"lastGeneratedColumn",null)}),a=this._originalMappings[++s];else for(var c=a.originalColumn;a&&a.originalLine===n&&a.originalColumn==c;)i.push({line:r.getArg(a,"generatedLine",null),column:r.getArg(a,"generatedColumn",null),lastColumn:r.getArg(a,"lastGeneratedColumn",null)}),a=this._originalMappings[++s]}return i},n.SourceMapConsumer=u,c.prototype=Object.create(u.prototype),c.prototype.consumer=u,c.fromSourceMap=function(e){var n=Object.create(c.prototype),t=n._names=i.fromArray(e._names.toArray(),!0),o=n._sources=i.fromArray(e._sources.toArray(),!0);n.sourceRoot=e._sourceRoot,n.sourcesContent=e._generateSourcesContent(n._sources.toArray(),n.sourceRoot),n.file=e._file;for(var s=e._mappings.toArray().slice(),u=n.__generatedMappings=[],f=n.__originalMappings=[],p=0,g=s.length;p<g;p++){var h=s[p],m=new l;m.generatedLine=h.generatedLine,m.generatedColumn=h.generatedColumn,h.source&&(m.source=o.indexOf(h.source),m.originalLine=h.originalLine,m.originalColumn=h.originalColumn,h.name&&(m.name=t.indexOf(h.name)),f.push(m)),u.push(m)}return a(n.__originalMappings,r.compareByOriginalPositions),n},c.prototype._version=3,Object.defineProperty(c.prototype,"sources",{get:function(){return this._sources.toArray().map(function(e){return null!=this.sourceRoot?r.join(this.sourceRoot,e):e},this)}}),c.prototype._parseMappings=function(e,n){for(var t,o,i,u,c,f=1,p=0,g=0,h=0,m=0,d=0,_=e.length,v=0,y={},b={},w=[],C=[];v<_;)if(";"===e.charAt(v))f++,v++,p=0;else if(","===e.charAt(v))v++;else{for((t=new l).generatedLine=f,u=v;u<_&&!this._charIsMappingSeparator(e,u);u++);if(i=y[o=e.slice(v,u)])v+=o.length;else{for(i=[];v<u;)s.decode(e,v,b),c=b.value,v=b.rest,i.push(c);if(2===i.length)throw new Error("Found a source, but no line and column");if(3===i.length)throw new Error("Found a source and line, but no column");y[o]=i}t.generatedColumn=p+i[0],p=t.generatedColumn,i.length>1&&(t.source=m+i[1],m+=i[1],t.originalLine=g+i[2],g=t.originalLine,t.originalLine+=1,t.originalColumn=h+i[3],h=t.originalColumn,i.length>4&&(t.name=d+i[4],d+=i[4])),C.push(t),"number"==typeof t.originalLine&&w.push(t)}a(C,r.compareByGeneratedPositionsDeflated),this.__generatedMappings=C,a(w,r.compareByOriginalPositions),this.__originalMappings=w},c.prototype._findMapping=function(e,n,t,r,i,s){if(e[t]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[t]);if(e[r]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[r]);return o.search(e,n,i,s)},c.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var n=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var t=this._generatedMappings[e+1];if(n.generatedLine===t.generatedLine){n.lastGeneratedColumn=t.generatedColumn-1;continue}}n.lastGeneratedColumn=1/0}},c.prototype.originalPositionFor=function(e){var n={generatedLine:r.getArg(e,"line"),generatedColumn:r.getArg(e,"column")},t=this._findMapping(n,this._generatedMappings,"generatedLine","generatedColumn",r.compareByGeneratedPositionsDeflated,r.getArg(e,"bias",u.GREATEST_LOWER_BOUND));if(t>=0){var o=this._generatedMappings[t];if(o.generatedLine===n.generatedLine){var i=r.getArg(o,"source",null);null!==i&&(i=this._sources.at(i),null!=this.sourceRoot&&(i=r.join(this.sourceRoot,i)));var s=r.getArg(o,"name",null);return null!==s&&(s=this._names.at(s)),{source:i,line:r.getArg(o,"originalLine",null),column:r.getArg(o,"originalColumn",null),name:s}}}return{source:null,line:null,column:null,name:null}},c.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(e){return null==e})},c.prototype.sourceContentFor=function(e,n){if(!this.sourcesContent)return null;if(null!=this.sourceRoot&&(e=r.relative(this.sourceRoot,e)),this._sources.has(e))return this.sourcesContent[this._sources.indexOf(e)];var t;if(null!=this.sourceRoot&&(t=r.urlParse(this.sourceRoot))){var o=e.replace(/^file:\/\//,"");if("file"==t.scheme&&this._sources.has(o))return this.sourcesContent[this._sources.indexOf(o)];if((!t.path||"/"==t.path)&&this._sources.has("/"+e))return this.sourcesContent[this._sources.indexOf("/"+e)]}if(n)return null;throw new Error('"'+e+'" is not in the SourceMap.')},c.prototype.generatedPositionFor=function(e){var n=r.getArg(e,"source");if(null!=this.sourceRoot&&(n=r.relative(this.sourceRoot,n)),!this._sources.has(n))return{line:null,column:null,lastColumn:null};var t={source:n=this._sources.indexOf(n),originalLine:r.getArg(e,"line"),originalColumn:r.getArg(e,"column")},o=this._findMapping(t,this._originalMappings,"originalLine","originalColumn",r.compareByOriginalPositions,r.getArg(e,"bias",u.GREATEST_LOWER_BOUND));if(o>=0){var i=this._originalMappings[o];if(i.source===t.source)return{line:r.getArg(i,"generatedLine",null),column:r.getArg(i,"generatedColumn",null),lastColumn:r.getArg(i,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},n.BasicSourceMapConsumer=c,f.prototype=Object.create(u.prototype),f.prototype.constructor=u,f.prototype._version=3,Object.defineProperty(f.prototype,"sources",{get:function(){for(var e=[],n=0;n<this._sections.length;n++)for(var t=0;t<this._sections[n].consumer.sources.length;t++)e.push(this._sections[n].consumer.sources[t]);return e}}),f.prototype.originalPositionFor=function(e){var n={generatedLine:r.getArg(e,"line"),generatedColumn:r.getArg(e,"column")},t=o.search(n,this._sections,function(e,n){return e.generatedLine-n.generatedOffset.generatedLine||e.generatedColumn-n.generatedOffset.generatedColumn}),i=this._sections[t];return i?i.consumer.originalPositionFor({line:n.generatedLine-(i.generatedOffset.generatedLine-1),column:n.generatedColumn-(i.generatedOffset.generatedLine===n.generatedLine?i.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},f.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(e){return e.consumer.hasContentsOfAllSources()})},f.prototype.sourceContentFor=function(e,n){for(var t=0;t<this._sections.length;t++){var r=this._sections[t].consumer.sourceContentFor(e,!0);if(r)return r}if(n)return null;throw new Error('"'+e+'" is not in the SourceMap.')},f.prototype.generatedPositionFor=function(e){for(var n=0;n<this._sections.length;n++){var t=this._sections[n];if(-1!==t.consumer.sources.indexOf(r.getArg(e,"source"))){var o=t.consumer.generatedPositionFor(e);if(o)return{line:o.line+(t.generatedOffset.generatedLine-1),column:o.column+(t.generatedOffset.generatedLine===o.line?t.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},f.prototype._parseMappings=function(e,n){this.__generatedMappings=[],this.__originalMappings=[];for(var t=0;t<this._sections.length;t++)for(var o=this._sections[t],i=o.consumer._generatedMappings,s=0;s<i.length;s++){var u=i[s],c=o.consumer._sources.at(u.source);null!==o.consumer.sourceRoot&&(c=r.join(o.consumer.sourceRoot,c)),this._sources.add(c),c=this._sources.indexOf(c);var l=o.consumer._names.at(u.name);this._names.add(l),l=this._names.indexOf(l);var f={source:c,generatedLine:u.generatedLine+(o.generatedOffset.generatedLine-1),generatedColumn:u.generatedColumn+(o.generatedOffset.generatedLine===u.generatedLine?o.generatedOffset.generatedColumn-1:0),originalLine:u.originalLine,originalColumn:u.originalColumn,name:l};this.__generatedMappings.push(f),"number"==typeof f.originalLine&&this.__originalMappings.push(f)}a(this.__generatedMappings,r.compareByGeneratedPositionsDeflated),a(this.__originalMappings,r.compareByOriginalPositions)},n.IndexedSourceMapConsumer=f},function(e,n){n.GREATEST_LOWER_BOUND=1,n.LEAST_UPPER_BOUND=2,n.search=function(e,t,r,o){if(0===t.length)return-1;var i=function e(t,r,o,i,s,a){var u=Math.floor((r-t)/2)+t,c=s(o,i[u],!0);return 0===c?u:c>0?r-u>1?e(u,r,o,i,s,a):a==n.LEAST_UPPER_BOUND?r<i.length?r:-1:u:u-t>1?e(t,u,o,i,s,a):a==n.LEAST_UPPER_BOUND?u:t<0?-1:t}(-1,t.length,e,t,r,o||n.GREATEST_LOWER_BOUND);if(i<0)return-1;for(;i-1>=0&&0===r(t[i],t[i-1],!0);)--i;return i}},function(e,n,t){var r=t(0),o=Object.prototype.hasOwnProperty;function i(){this._array=[],this._set=Object.create(null)}i.fromArray=function(e,n){for(var t=new i,r=0,o=e.length;r<o;r++)t.add(e[r],n);return t},i.prototype.size=function(){return Object.getOwnPropertyNames(this._set).length},i.prototype.add=function(e,n){var t=r.toSetString(e),i=o.call(this._set,t),s=this._array.length;i&&!n||this._array.push(e),i||(this._set[t]=s)},i.prototype.has=function(e){var n=r.toSetString(e);return o.call(this._set,n)},i.prototype.indexOf=function(e){var n=r.toSetString(e);if(o.call(this._set,n))return this._set[n];throw new Error('"'+e+'" is not in the set.')},i.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},i.prototype.toArray=function(){return this._array.slice()},n.ArraySet=i},function(e,n,t){var r=t(5);n.encode=function(e){var n,t="",o=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{n=31&o,(o>>>=5)>0&&(n|=32),t+=r.encode(n)}while(o>0);return t},n.decode=function(e,n,t){var o,i,s,a,u=e.length,c=0,l=0;do{if(n>=u)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(i=r.decode(e.charCodeAt(n++))))throw new Error("Invalid base64 digit: "+e.charAt(n-1));o=!!(32&i),c+=(i&=31)<<l,l+=5}while(o);t.value=(a=(s=c)>>1,1==(1&s)?-a:a),t.rest=n}},function(e,n){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");n.encode=function(e){if(0<=e&&e<t.length)return t[e];throw new TypeError("Must be between 0 and 63: "+e)},n.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}},function(e,n){function t(e,n,t){var r=e[n];e[n]=e[t],e[t]=r}n.quickSort=function(e,n){!function e(n,r,o,i){if(o<i){var s=o-1;t(n,(l=o,f=i,Math.round(l+Math.random()*(f-l))),i);for(var a=n[i],u=o;u<i;u++)r(n[u],a)<=0&&t(n,s+=1,u);t(n,s+1,u);var c=s+1;e(n,r,o,c-1),e(n,r,c+1,i)}var l,f}(e,n,0,e.length-1)}}]);!function(e,n){"use strict";"function"==typeof define&&define.amd?define("stacktrace-gps",["source-map","stackframe"],n):"object"==typeof exports?module.exports=n(require("source-map/lib/source-map-consumer"),require("stackframe")):e.StackTraceGPS=n(e.SourceMap||e.sourceMap,e.StackFrame)}(this,function(e,n){"use strict";function t(e){return new Promise(function(n,t){var r=new XMLHttpRequest;r.open("get",e),r.onerror=t,r.onreadystatechange=function(){4===r.readyState&&(r.status>=200&&r.status<300||"file://"===e.substr(0,7)&&r.responseText?n(r.responseText):t(new Error("HTTP status: "+r.status+" retrieving "+e)))},r.send()})}function r(e){if("undefined"!=typeof window&&window.atob)return window.atob(e);throw new Error("You must supply a polyfill for window.atob in this environment")}function o(e){if("object"!=typeof e)throw new TypeError("Given StackFrame is not an object");if("string"!=typeof e.fileName)throw new TypeError("Given file name is not a String");if("number"!=typeof e.lineNumber||e.lineNumber%1!=0||e.lineNumber<1)throw new TypeError("Given line number must be a positive integer");if("number"!=typeof e.columnNumber||e.columnNumber%1!=0||e.columnNumber<0)throw new TypeError("Given column number must be a non-negative integer");return!0}return function i(s){if(!(this instanceof i))return new i(s);s=s||{},this.sourceCache=s.sourceCache||{},this.sourceMapConsumerCache=s.sourceMapConsumerCache||{},this.ajax=s.ajax||t,this._atob=s.atob||r,this._get=function(e){return new Promise(function(n,t){var r="data:"===e.substr(0,5);if(this.sourceCache[e])n(this.sourceCache[e]);else if(s.offline&&!r)t(new Error("Cannot make network requests in offline mode"));else if(r){var o=e.match(/^data:application\/json;([\w=:"-]+;)*base64,/);if(o){var i=o[0].length,a=e.substr(i),u=this._atob(a);this.sourceCache[e]=u,n(u)}else t(new Error("The encoding of the inline sourcemap is not supported"))}else{var c=this.ajax(e,{method:"get"});this.sourceCache[e]=c,c.then(n,t)}}.bind(this))},this._getSourceMapConsumer=function(n,t){return new Promise(function(r){if(this.sourceMapConsumerCache[n])r(this.sourceMapConsumerCache[n]);else{var o=new Promise(function(r,o){return this._get(n).then(function(n){"string"==typeof n&&(n=function(e){if("undefined"!=typeof JSON&&JSON.parse)return JSON.parse(e);throw new Error("You must supply a polyfill for JSON.parse in this environment")}(n.replace(/^\)\]\}'/,""))),void 0===n.sourceRoot&&(n.sourceRoot=t),r(new e.SourceMapConsumer(n))}).catch(o)}.bind(this));this.sourceMapConsumerCache[n]=o,r(o)}}.bind(this))},this.pinpoint=function(e){return new Promise(function(n,t){this.getMappedLocation(e).then(function(e){function t(){n(e)}this.findFunctionName(e).then(n,t).catch(t)}.bind(this),t)}.bind(this))},this.findFunctionName=function(e){return new Promise(function(t,r){o(e),this._get(e.fileName).then(function(r){var o=e.lineNumber,i=e.columnNumber,s=function(e,n){for(var t=[/['"]?([$_A-Za-z][$_A-Za-z0-9]*)['"]?\s*[:=]\s*function\b/,/function\s+([^('"`]*?)\s*\(([^)]*)\)/,/['"]?([$_A-Za-z][$_A-Za-z0-9]*)['"]?\s*[:=]\s*(?:eval|new Function)\b/,/\b(?!(?:if|for|switch|while|with|catch)\b)(?:(?:static)\s+)?(\S+)\s*\(.*?\)\s*\{/,/['"]?([$_A-Za-z][$_A-Za-z0-9]*)['"]?\s*[:=]\s*\(.*?\)\s*=>/],r=e.split("\n"),o="",i=Math.min(n,20),s=0;s<i;++s){var a=r[n-s-1],u=a.indexOf("//");if(u>=0&&(a=a.substr(0,u)),a){o=a+o;for(var c=t.length,l=0;l<c;l++){var f=t[l].exec(o);if(f&&f[1])return f[1]}}}}(r,o);t(s?new n({functionName:s,args:e.args,fileName:e.fileName,lineNumber:o,columnNumber:i}):e)},r).catch(r)}.bind(this))},this.getMappedLocation=function(e){return new Promise(function(t,r){!function(){if("function"!=typeof Object.defineProperty||"function"!=typeof Object.create)throw new Error("Unable to consume source maps in older browsers")}(),o(e);var i=this.sourceCache,s=e.fileName;this._get(s).then(function(r){var o=function(e){for(var n,t,r=/\/\/[#@] ?sourceMappingURL=([^\s'"]+)\s*$/gm;t=r.exec(e);)n=t[1];if(n)return n;throw new Error("sourceMappingURL not found")}(r),a="data:"===o.substr(0,5),u=s.substring(0,s.lastIndexOf("/")+1);return"/"===o[0]||a||/^https?:\/\/|^\/\//i.test(o)||(o=u+o),this._getSourceMapConsumer(o,u).then(function(r){return function(e,t,r){return new Promise(function(o,i){var s=t.originalPositionFor({line:e.lineNumber,column:e.columnNumber});if(s.source){var a=t.sourceContentFor(s.source);a&&(r[s.source]=a),o(new n({functionName:s.name||e.functionName,args:e.args,fileName:s.source,lineNumber:s.line,columnNumber:s.column}))}else i(new Error("Could not get original source for given stackframe and source map"))})}(e,r,i).then(t).catch(function(){t(e)})})}.bind(this),r).catch(r)}.bind(this))}}});
//# sourceMappingURL=stacktrace-gps-polyfilled.min.js.map