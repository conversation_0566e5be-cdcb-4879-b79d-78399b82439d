{"name": "swap-case", "version": "1.1.2", "description": "Swap the case of a string", "main": "swap-case.js", "typings": "swap-case.d.ts", "files": ["swap-case.js", "swap-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/swap-case.git"}, "keywords": ["swap", "case", "reverse", "switch"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/swap-case/issues"}, "homepage": "https://github.com/blakeembrey/swap-case", "devDependencies": {"istanbul": "^0.3.0", "mocha": "^2.2.1", "pre-commit": "^1.0.6", "standard": "^3.3.0"}, "dependencies": {"lower-case": "^1.1.1", "upper-case": "^1.1.1"}}