{"version": 3, "file": "Data3DTexture.cjs", "sources": ["../../src/_polyfill/Data3DTexture.js"], "sourcesContent": ["import { Texture, ClampToEdgeWrapping, NearestFilter } from 'three'\n\nclass Data3DTexture extends Texture {\n  constructor(data = null, width = 1, height = 1, depth = 1) {\n    super(null)\n\n    this.isData3DTexture = true\n\n    this.image = { data, width, height, depth }\n\n    this.magFilter = NearestFilter\n    this.minFilter = NearestFilter\n\n    this.wrapR = ClampToEdgeWrapping\n\n    this.generateMipmaps = false\n    this.flipY = false\n    this.unpackAlignment = 1\n  }\n}\n\nexport { Data3DTexture }\n"], "names": ["Texture", "NearestFilter", "ClampToEdgeWrapping"], "mappings": ";;;AAEA,MAAM,sBAAsBA,MAAAA,QAAQ;AAAA,EAClC,YAAY,OAAO,MAAM,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG;AACzD,UAAM,IAAI;AAEV,SAAK,kBAAkB;AAEvB,SAAK,QAAQ,EAAE,MAAM,OAAO,QAAQ,MAAO;AAE3C,SAAK,YAAYC,MAAa;AAC9B,SAAK,YAAYA,MAAa;AAE9B,SAAK,QAAQC,MAAmB;AAEhC,SAAK,kBAAkB;AACvB,SAAK,QAAQ;AACb,SAAK,kBAAkB;AAAA,EACxB;AACH;;"}