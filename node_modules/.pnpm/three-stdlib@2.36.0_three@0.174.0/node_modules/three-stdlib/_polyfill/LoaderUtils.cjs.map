{"version": 3, "file": "LoaderUtils.cjs", "sources": ["../../src/_polyfill/LoaderUtils.js"], "sourcesContent": ["export function decodeText(array) {\n  if (typeof TextDecoder !== 'undefined') {\n    return new TextDecoder().decode(array)\n  }\n\n  // Avoid the String.fromCharCode.apply(null, array) shortcut, which\n  // throws a \"maximum call stack size exceeded\" error for large arrays.\n\n  let s = ''\n\n  for (let i = 0, il = array.length; i < il; i++) {\n    // Implicitly assumes little-endian.\n    s += String.fromCharCode(array[i])\n  }\n\n  try {\n    // merges multi-byte utf-8 characters.\n\n    return decodeURIComponent(escape(s))\n  } catch (e) {\n    // see https://github.com/mrdoob/three.js/issues/16358\n\n    return s\n  }\n}\n"], "names": [], "mappings": ";;AAAO,SAAS,WAAW,OAAO;AAChC,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO,IAAI,YAAW,EAAG,OAAO,KAAK;AAAA,EACtC;AAKD,MAAI,IAAI;AAER,WAAS,IAAI,GAAG,KAAK,MAAM,QAAQ,IAAI,IAAI,KAAK;AAE9C,SAAK,OAAO,aAAa,MAAM,CAAC,CAAC;AAAA,EAClC;AAED,MAAI;AAGF,WAAO,mBAAmB,OAAO,CAAC,CAAC;AAAA,EACpC,SAAQ,GAAP;AAGA,WAAO;AAAA,EACR;AACH;;"}