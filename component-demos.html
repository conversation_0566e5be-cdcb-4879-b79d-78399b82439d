<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组件方案演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            padding: 40px 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 8px;
        }
        
        .demo-subtitle {
            color: #718096;
            margin-bottom: 32px;
        }
        
        /* 方案A：时间线流程展示 */
        .timeline {
            position: relative;
            padding-left: 40px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e2e8f0;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 32px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -28px;
            top: 8px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #4299e1;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #4299e1;
        }
        
        .timeline-icon {
            position: absolute;
            left: -32px;
            top: 4px;
            width: 24px;
            height: 24px;
            background: #4299e1;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        
        .timeline-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .timeline-desc {
            color: #718096;
            margin-bottom: 4px;
        }
        
        /* 方案B：步骤指引展示 */
        .steps {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
        }
        
        .step {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4299e1;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 16px;
        }
        
        .step-content h3 {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .step-content p {
            color: #718096;
            font-size: 14px;
        }
        
        .step-connector {
            flex: 1;
            height: 2px;
            background: #e2e8f0;
            margin: 0 20px;
        }
        
        /* 方案C：特性卡片展示 */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
        }
        
        .feature-card {
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            transition: all 0.2s;
        }
        
        .feature-card:hover {
            border-color: #4299e1;
            box-shadow: 0 8px 25px -8px rgba(66, 153, 225, 0.3);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .icon-badge {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: #ebf8ff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 24px;
            color: #4299e1;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .card-status {
            font-size: 12px;
            color: #38a169;
            background: #f0fff4;
            padding: 2px 8px;
            border-radius: 4px;
        }
        
        .card-desc {
            color: #718096;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; margin-bottom: 48px; color: #2d3748; font-size: 32px;">
            智能投放系统 - 组件方案演示
        </h1>
        
        <!-- 方案A：时间线流程展示 -->
        <div class="demo-section">
            <h2 class="demo-title">方案A：时间线流程展示</h2>
            <p class="demo-subtitle">展示流量聚合的完整流程步骤</p>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-icon">📱</div>
                    <div class="timeline-title">客户提交需求</div>
                    <div class="timeline-desc">用户通过微信小程序填写保险需求信息</div>
                    <div class="timeline-desc">• 选择保险类型（车险、财险等）</div>
                    <div class="timeline-desc">• 填写基本信息和联系方式</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">🤖</div>
                    <div class="timeline-title">智能算法匹配</div>
                    <div class="timeline-desc">系统自动分析客户需求，匹配最适合的服务商</div>
                    <div class="timeline-desc">• 基于地理位置就近匹配</div>
                    <div class="timeline-desc">• 根据服务商专业度排序</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">🔔</div>
                    <div class="timeline-title">实时推送通知</div>
                    <div class="timeline-desc">向匹配的服务商实时推送客户需求</div>
                    <div class="timeline-desc">• 多渠道通知（短信、微信、APP）</div>
                    <div class="timeline-desc">• 5分钟内必须响应</div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-icon">📞</div>
                    <div class="timeline-title">服务商响应</div>
                    <div class="timeline-desc">服务商主动联系客户，提供专业服务</div>
                    <div class="timeline-desc">• 平均响应时间：2分钟</div>
                    <div class="timeline-desc">• 客户满意度：98.5%</div>
                </div>
            </div>
        </div>
        
        <!-- 方案B：步骤指引展示 -->
        <div class="demo-section">
            <h2 class="demo-title">方案B：步骤指引展示</h2>
            <p class="demo-subtitle">清晰展示用户使用流程的关键步骤</p>
            
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>需求提交</h3>
                        <p>微信小程序一键提交</p>
                    </div>
                </div>
                <div class="step-connector"></div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>智能匹配</h3>
                        <p>AI算法精准匹配服务商</p>
                    </div>
                </div>
                <div class="step-connector"></div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>快速响应</h3>
                        <p>2分钟内专业服务到位</p>
                    </div>
                </div>
            </div>
            
            <div style="background: #f7fafc; padding: 24px; border-radius: 8px; border-left: 4px solid #4299e1;">
                <h4 style="color: #2d3748; margin-bottom: 8px;">💡 核心优势</h4>
                <p style="color: #718096;">通过微信生态聚合保险需求流量，智能分发匹配优质服务商，数据驱动优化投放效果，打造多方共赢的保险服务生态平台。</p>
            </div>
        </div>
        
        <!-- 方案C：特性卡片展示 -->
        <div class="demo-section">
            <h2 class="demo-title">方案C：特性卡片展示</h2>
            <p class="demo-subtitle">突出展示核心功能特性和优势</p>
            
            <div class="cards-grid">
                <div class="feature-card">
                    <div class="card-header">
                        <div class="icon-badge">📊</div>
                        <div>
                            <div class="card-title">流量聚合</div>
                            <div class="card-status">微信生态</div>
                        </div>
                    </div>
                    <div class="card-desc">
                        通过微信小程序聚合车险、财险等多种保险需求，基于微信生态的天然优势，降低用户获取成本，提升转化效率。
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="card-header">
                        <div class="icon-badge">🎯</div>
                        <div>
                            <div class="card-title">智能匹配</div>
                            <div class="card-status">AI驱动</div>
                        </div>
                    </div>
                    <div class="card-desc">
                        基于大数据算法实现客户需求与服务商的精准匹配，提升转化效率和客户满意度，平均匹配成功率达85%。
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="card-header">
                        <div class="icon-badge">📈</div>
                        <div>
                            <div class="card-title">数据分析</div>
                            <div class="card-status">实时监控</div>
                        </div>
                    </div>
                    <div class="card-desc">
                        实时监控投放数据，提供详细的分析报告，帮助优化投放策略和提升ROI，数据准确率99.9%。
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
