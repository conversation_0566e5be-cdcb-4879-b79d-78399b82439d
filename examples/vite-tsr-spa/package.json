{"name": "vite-tsr", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "generate": "tsr generate", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "imports": {"#*": "./src/*"}, "dependencies": {"@chakra-ui/next-js": "^2.2.0", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@saas-ui/auth": "^3.0.0", "@saas-ui/react": "^2.5.9", "@saas-ui/supabase": "^2.1.3", "@supabase/supabase-js": "^2.39.6", "@tanstack/react-query": "^5.21.2", "@tanstack/react-query-devtools": "^5.21.3", "@tanstack/react-router": "^1.16.5", "@tanstack/router-cli": "^1.16.5", "@tanstack/router-devtools": "^1.16.5", "@tanstack/router-vite-plugin": "^1.16.5", "framer-motion": "^11.0.5", "prettier": "^3.2.5", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.1", "@typescript-eslint/parser": "^7.0.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.3.3", "vite": "^5.1.3"}}