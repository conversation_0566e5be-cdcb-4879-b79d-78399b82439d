{"name": "saas-ui-clerk-auth-ts", "version": "1.0.0", "private": true, "scripts": {"dev": "next", "build": "next build", "start": "next start", "type-check": "tsc"}, "dependencies": {"@chakra-ui/react": "^2.10.1", "@clerk/nextjs": "^6.0.2", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@saas-ui/auth": "^3.6.0", "@saas-ui/clerk": "^4.0.0", "@saas-ui/react": "^2.11.1", "framer-motion": "^11.9.0", "next": "^14.2.14", "nodemailer": "^6.7.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "typescript": "^5.6.3"}, "devDependencies": {"@types/node": "^20.11.18", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1"}, "repository": {"type": "git", "url": "https://github.com/saas-js/saas-ui.git", "directory": "examples/nextjs-typescript"}, "bugs": {"url": "https://github.com/saas-js/saas-ui/issues"}, "license": "MIT"}