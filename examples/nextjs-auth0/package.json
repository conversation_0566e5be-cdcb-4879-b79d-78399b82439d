{"name": "saas-ui-nextjs-auth0-ts", "version": "1.0.0", "private": true, "scripts": {"dev": "next", "build": "next build", "start": "next start", "type-check": "tsc"}, "dependencies": {"@auth0/auth0-spa-js": "^2.1.3", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@saas-ui/assets": "^1.0.0", "@saas-ui/auth": "^3.0.6", "@saas-ui/auth0": "^0.1.0", "@saas-ui/react": "^2.5.5", "framer-motion": "^11.0.3", "next": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "typescript": "^5.3.3"}, "devDependencies": {"@types/node": "^20.11.10", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18"}, "repository": {"type": "git", "url": "https://github.com/saas-js/saas-ui.git", "directory": "examples/nextjs-auth0"}, "bugs": {"url": "https://github.com/saas-js/saas-ui/issues"}, "license": "MIT"}