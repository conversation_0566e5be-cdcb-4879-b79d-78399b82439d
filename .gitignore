# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager lock files (keep pnpm-lock.yaml for consistency)
package-lock.json
yarn.lock

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.output/
.vercel/
.netlify/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Storybook build outputs
storybook-static

# Temporary folders
tmp/
temp/

# Turbo
.turbo

# Changeset (keep .changeset directory but ignore generated files)
.changeset/*.md
.changeset/pre.json

# Testing
.jest/
coverage/
test-results/

# Storybook
storybook-static/
chromatic-build/

# Next.js
.next/
out/

# Vercel
.vercel/

# Local development
.local

# TypeScript
*.tsbuildinfo
.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore

# Husky
.husky/_/

# pnpm
.pnpm-debug.log*

# Backup files
*.bak
*.backup

# macOS
.DS_Store

# Windows
Thumbs.db

# Linux
*~

# Temporary files
*.tmp
*.temp

# Editor files
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.vscode/extensions.json
!.vscode/settings.json.example
!.vscode/launch.json.example
!.vscode/tasks.json.example
!.vscode/extensions.json.example
